'use client';

interface NavbarProps {
  onCategoryChange: (category: string) => void;
  activeCategory: string;
  categories: string[];
}

const Navbar = ({
  onCategoryChange,
  activeCategory,
  categories,
}: NavbarProps) => {
  const handleCategoryClick = (category: string) => {
    onCategoryChange(category);
  };

  return (
    <div className="mt-[3vh] flex w-full justify-center py-6 md:mt-[10vh]">
      <div className="no-scrollbar flex h-[47px] w-[90%] flex-nowrap items-center gap-[5vw] overflow-x-auto overflow-y-hidden rounded-full bg-white px-6 py-1 shadow-lg/20 md:w-[65vw] md:justify-evenly md:gap-[2vw] 2xl:h-[68px]">
        {categories.map((category) => (
          <button
            key={category}
            onClick={() => handleCategoryClick(category)}
            className={`relative flex-shrink-0 cursor-pointer px-2 text-[16px] whitespace-nowrap transition-all duration-200 ease-in-out md:px-0 md:font-medium 2xl:text-base 2xl:text-[24px] ${
              activeCategory === category ? 'text-[#FF5542]' : 'text-[#047aff]'
            } `}
          >
            {category}
          </button>
        ))}
      </div>
    </div>
  );
};

export default Navbar;
