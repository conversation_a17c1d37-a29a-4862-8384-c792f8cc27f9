'use client';

import DropDown from '@hi7/assets/icon/chevron-down-white.svg';
import type { MenuLinkProps } from '@hi7/interface/link';
import clsx from 'clsx';
import type { Variants } from 'framer-motion';
import { AnimatePresence, motion } from 'framer-motion';
import { usePathname } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import Link from '../Link';

const submenuVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.95,
    transition: { duration: 0.2, ease: 'easeOut' },
  },
  visible: {
    opacity: 1,
    scale: 1,
    transition: { duration: 0.2, ease: 'easeIn' },
  },
};

const SubMenuLink = ({
  url,
  children,
  items = [],
}: OmitStrict<MenuLinkProps, 'asButton'>) => {
  const pathname = usePathname();
  const hasSubitem = items.length > 0;
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  const handleClick = () => {
    setIsOpen(!isOpen);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div
      ref={menuRef}
      onClick={handleClick}
      className={clsx(
        pathname === url || pathname.includes(url) ? 'text-hi7-primary' : '',
        'relative flex cursor-pointer items-center gap-2.5',
      )}
    >
      {children}

      {hasSubitem && (
        <>
          <DropDown />

          <AnimatePresence>
            {isOpen && (
              <motion.div
                initial="hidden"
                animate="visible"
                exit="hidden"
                variants={submenuVariants}
                className={clsx(
                  'absolute top-full left-0 z-50 mt-7 grid h-fit min-h-fit w-[680px] grid-cols-2 overflow-hidden rounded-[20px] bg-white pt-3 pb-3',
                )}
              >
                {items.map(({ url, text, icon }) => (
                  <Link
                    url={url}
                    key={url}
                    className={clsx(
                      'flex items-center gap-2 whitespace-nowrap',
                      'cursor-pointer text-start text-[#047AFF]',
                      'px-[24px] py-3',
                      'transition-all duration-300',
                      'w-full min-w-[155px] cursor-pointer',
                    )}
                  >
                    <i className="text-[#FF5542]">{icon}</i>
                    {text}
                  </Link>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </>
      )}
    </div>
  );
};

export default SubMenuLink;
