'use client';

import Feature1 from '@hi7/assets/icon/feature1.svg';
import Feature3 from '@hi7/assets/icon/feature6.svg';
import Feature2 from '@hi7/assets/icon/feature9.svg';
import useScreenSize from '@hi7/helpers/useScreenSize';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { motion } from 'motion/react';
import { Arsenal } from 'next/font/google';
import { useEffect, useRef, useState } from 'react';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'auto',
});

interface FeatureProps extends DictionaryProps {
  isActive?: boolean;
}

function Feature({ dictionary }: FeatureProps) {
  const [isExpanding, setIsExpanding] = useState(false);
  const [contentVisible, setContentVisible] = useState(true);
  const sectionRef = useRef<HTMLDivElement>(null);
  const { isMobile } = useScreenSize();

  useEffect(() => {
    const handleExpandMargin = () => {
      setContentVisible(false);
      setTimeout(() => {
        setIsExpanding(true);
      }, 200);
    };

    const handleResetMargin = () => {
      console.log('FeatureTwo: Resetting margin animation');
      setIsExpanding(false);
      setContentVisible(true);
    };

    window.addEventListener('featureTwo:expandMargin', handleExpandMargin);
    window.addEventListener('featureTwo:resetMargin', handleResetMargin);

    return () => {
      window.removeEventListener('featureTwo:expandMargin', handleExpandMargin);
      window.removeEventListener('featureTwo:resetMargin', handleResetMargin);
    };
  }, []);

  useEffect(() => {
    setIsExpanding(false);
    setContentVisible(true);
  }, []);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        setIsExpanding(false);
        setContentVisible(true);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  const features = [
    {
      icon: Feature1,
      title:
        dictionary.features.globalDataAcquisition.feature2.globalDataAcquisition
          .title,
      desc: dictionary.features.globalDataAcquisition.feature2
        .globalDataAcquisition.desc,
    },
    {
      icon: Feature2,
      title:
        dictionary.features.globalDataAcquisition.feature2
          .customNumberSegmentation.title,
      desc: dictionary.features.globalDataAcquisition.feature2
        .customNumberSegmentation.desc,
    },
    {
      icon: Feature3,
      title:
        dictionary.features.globalDataAcquisition.feature2
          .globalNumberGeneration.title,
      desc: dictionary.features.globalDataAcquisition.feature2
        .globalNumberGeneration.desc,
    },
  ];

  return (
    <motion.div
      ref={sectionRef}
      className="relative z-10 flex translate-y-[5%] items-center justify-center overflow-hidden rounded-[30px] bg-[#047AFF] px-8 lg:translate-y-[10%] lg:rounded-[80px] lg:px-12 xl:mt-25 xl:translate-y-[10%]"
      initial={{
        marginLeft: '4rem',
        marginRight: '4rem',
        marginBottom: '3.75rem',
        y: '0%',
      }}
      animate={{
        marginLeft: isExpanding || isMobile ? '0rem' : '4rem',
        marginRight: isExpanding || isMobile ? '0rem' : '4rem',
        marginBottom: isExpanding || isMobile ? '0rem' : '3.75rem',
        y: isExpanding ? '5%' : '0%',
      }}
      transition={{
        duration: 0.5,
        ease: 'easeInOut',
      }}
    >
      <div className="h-full w-full lg:h-[88vh]">
        <div className="relative">
          <motion.div
            initial={{ opacity: 1 }}
            animate={{ opacity: contentVisible ? 1 : 0 }}
            transition={{ duration: 0.2, ease: 'easeInOut' }}
          >
            <div className="w-full">
              <div className="flex flex-col items-start justify-center pt-12 pb-15 text-white lg:items-start lg:px-10 lg:pt-20 lg:pb-12 xl:px-20 xl:pt-40">
                <h2
                  className={`mb-8 text-[40px] leading-[40px] font-bold lg:mb-0 lg:translate-y-[-30%] lg:text-[4vw] lg:leading-[58px] xl:mt-7 ${arsenal.className}`}
                >
                  {dictionary.features.globalDataAcquisition.feature2.title}
                </h2>
                <p className="w-[70%] text-[16px] font-thin whitespace-pre-line lg:text-[1.5vw] lg:font-[300] xl:my-3 xl:w-[60%]">
                  {dictionary.features.globalDataAcquisition.feature2.desc}
                </p>
                <hr className="mt-5 w-full lg:my-6" />
                <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:mt-5 lg:grid-cols-3 lg:gap-7 xl:gap-13">
                  {features.length > 0 &&
                    features.map((feature, index) => (
                      <div
                        key={index}
                        className="mb-0 flex w-full flex-col gap-4 rounded-[30px] bg-[#E9F3FF] p-7 text-[#047AFF] lg:rounded-[20px] lg:p-5 xl:p-10"
                      >
                        <div className="flex flex-row items-center justify-center lg:h-12 xl:justify-start">
                          <feature.icon className="w-[30%] xl:w-[18%] xl:scale-125" />
                          <h1 className="mt-1 text-[20px] leading-[25px] font-[500] lg:text-[20px] xl:text-[25px]">
                            {feature.title}
                          </h1>
                        </div>
                        <hr />
                        <span className="text-[16px] font-[300] xl:text-[22px] xl:font-[400]">
                          {feature.desc}
                        </span>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
}

export default Feature;
