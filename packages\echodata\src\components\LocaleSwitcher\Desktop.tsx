'use client';

import DropDown from '@hi7/assets/icon/dropdown.svg';
import { i18n, type Locale } from '@hi7/lib/i18n';
import { useGlobalStore } from '@hi7/provider/ZustandContext';
import clsx from 'clsx';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { DISPLAY_LANG, LANGUAGE_NAME } from './config';

export default function LocaleSwitcher() {
  const pathname = usePathname();
  const router = useRouter();
  const setBaseurl = useGlobalStore((s) => s.setBaseurl);
  const currentLocale = pathname.split('/')[1] as Locale;

  const handleLocaleChange = (locale: Locale) => {
    const segments = pathname.split('/');
    segments[1] = locale;
    const newPath = segments.join('/');
    setBaseurl(`/${locale}`);
    router.push(newPath);
  };

  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  const handleClick = () => {
    setIsOpen(!isOpen);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div
      ref={menuRef}
      onClick={handleClick}
      className={clsx('relative flex cursor-pointer items-center gap-2.5')}
    >
      <div className="flex items-center gap-[10px]">
        <div className="flex items-center gap-[10px] hover:text-[#047AFF]">
          {DISPLAY_LANG[currentLocale] ?? currentLocale}
          <DropDown />
        </div>
      </div>
      <svg className='w-3 h-3 -ml-10 font-black' fill='#04227D' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><path d='M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z' /></svg>

      {isOpen && (
        <div
          className={clsx(
            'rounded-[10px] absolute top-full right-0 z-50 mt-7 h-fit min-h-fit w-fit overflow-hidden bg-white rounded-[20px]',
          )}
        >
          {i18n.locales.map((locale) => (
            <button
              key={locale}
              onClick={() => handleLocaleChange(locale)}
              className={clsx(
                'flex items-center justify-center gap-2 whitespace-nowrap',
                'cursor-pointer text-center text-[#047AFF]',
                'px-[14px] py-4',
                'transition-all duration-300',
                'w-full min-w-[155px] cursor-pointer rounded-[5px]',
              )}
            >
              {LANGUAGE_NAME[locale] ?? locale}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
