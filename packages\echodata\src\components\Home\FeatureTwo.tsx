'use client';
// import Feature3 from '@hi7/assets/icon/feature5.svg';
import InsightImage from '@hi7/assets/background/insight-6.png';
import PolygonBg from '@hi7/assets/background/polygon-bg.png';
import Feature1 from '@hi7/assets/icon/feature6.svg?url';
import Feature3 from '@hi7/assets/icon/feature8.svg?url';
import Feature4 from '@hi7/assets/icon/feature9.svg?url';
import Feature2 from '@hi7/assets/icon/reason1.svg?url';
import { defaultAnimateConfig } from '@hi7/configs/animation';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { motion, useAnimation, useInView } from 'motion/react';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';
import { useEffect, useRef } from 'react';

import AnimationFrame from '../AnimationFrame';
import TriggerAnimation from '../TriggerAnimation';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
});

function Feature({ dictionary }: DictionaryProps) {
  const controls = useAnimation();
  const sentinelRef = useRef<HTMLDivElement | null>(null);
  const inView = useInView(sentinelRef, { amount: 0.05 });

  useEffect(() => {
    const localCfg = defaultAnimateConfig['SlideToRight3'];
    if (inView) {
      controls.start(localCfg.animate);
    } else {
      controls.set(localCfg.initial);
    }
  }, [inView, controls]);

  const features = [
    {
      icon: Feature1,
      title: dictionary.home.feature2.globalCoverage.title,
      desc: dictionary.home.feature2.globalCoverage.desc,
    },
    {
      icon: Feature2,
      title: dictionary.home.feature2.lowestPrices.title,
      desc: dictionary.home.feature2.lowestPrices.desc,
    },
    {
      icon: Feature3,
      title: dictionary.home.feature2.accurateFiltering.title,
      desc: dictionary.home.feature2.accurateFiltering.desc,
    },
    {
      icon: Feature4,
      title: dictionary.home.feature2.ecosystemIntegration.title,
      desc: dictionary.home.feature2.ecosystemIntegration.desc,
    },
  ];

  return (
    <TriggerAnimation>
      <section className="relative mt-20 flex flex-col items-center justify-between gap-10 overflow-x-clip bg-transparent px-10 py-10 text-[#047AFF] md:bg-[#ecffe9] lg:mt-5 lg:h-screen lg:flex-row lg:overflow-clip lg:bg-transparent lg:px-0 lg:pt-25 lg:pb-20">
        <svg
          className="pointer-events-none absolute inset-0 z-[-1] hidden lg:block"
          width="100%"
          height="105vh"
          viewBox="0 0 1441 856"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          preserveAspectRatio="none"
        >
          <path
            d="M0.5 126.95V161.11C0.5 227.91 54.6688 282.06 121.492 282.06H265C463 282.06 587.372 389 587.372 593C587.372 623.331 587.375 615.5 587.375 616.335C587.375 698 639 756 731.5 756H1289.5C1377.5 756 1441 695.5 1441 608V126.95C1441 60.1496 1441 85.9995 1441 85.9995H0.51001C0.51001 85.9995 0.51001 60.1496 0.51001 126.95H0.5Z"
            fill="#E9F3FF"
          />
          <path
            d="M1 80C1 121.974 35.0264 156 77 156H1365C1406.97 156 1441 121.974 1441 80C1441 38.0264 1406.97 4 1365 4H77C35.0263 4 1 38.0264 1 80Z"
            fill="#E9F3FF"
          />
        </svg>

        <AnimationFrame
          variant="SlideToRight2"
          once={false}
          className="pointer-events-none absolute inset-0 z-[-1] hidden lg:block"
        >
          <svg
            className=""
            width="100%"
            height="105vh"
            viewBox="0 0 1441 856"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            preserveAspectRatio="none"
          >
            <path
              d="M0.5 126.95V161.11C0.5 227.91 54.6688 282.06 121.492 282.06H265C463 282.06 587.372 389 587.372 593C587.372 623.331 587.375 615.5 587.375 616.335C587.375 698 639 756 731.5 756H1289.5C1377.5 756 1441 695.5 1441 608V126.95C1441 60.1496 1441 85.9995 1441 85.9995H0.51001C0.51001 85.9995 0.51001 60.1496 0.51001 126.95H0.5Z"
              fill="#E9F3FF"
            />
            <path
              d="M1 80C1 121.974 35.0264 156 77 156H1365C1406.97 156 1441 121.974 1441 80C1441 38.0264 1406.97 4 1365 4H77C35.0263 4 1 38.0264 1 80Z"
              fill="#E9F3FF"
            />
          </svg>
        </AnimationFrame>

        {/* <PolygonBg className="absolute inset-0 z-[-1] pointer-events-none md:hidden" /> */}

        <Image
          src={PolygonBg}
          alt={'insight-image'}
          className="pointer-events-none absolute inset-0 z-[-1] h-[100%] w-[100%] object-cover md:hidden"
        />
        <div className="relative mb-10 w-full pt-10 lg:mb-0 lg:w-1/2 lg:self-start lg:pt-0">
          <h2
            className={`mb-10 ml-10 hidden leading-[30px] font-bold ${arsenal.className} md:mb-5 md:flex md:text-[50px] lg:mt-3 lg:ml-40 lg:text-[54px] xl:mt-20 xl:pl-[130px] xl:text-[4vw] xl:leading-[40px]`}
          >
            {dictionary.home.feature2.title}{' '}
          </h2>
          <h2
            className={`mb-5 text-[35px] leading-[45px] font-bold whitespace-pre-line lg:text-5xl ${arsenal.className} md:hidden`}
          >
            {('title_mobile' in dictionary.home.feature2
              ? dictionary.home.feature2.title_mobile
              : dictionary.home.feature2.title
            )
              .split(' ')
              .map((line, i) => (
                <h2 key={i}>
                  {line}
                  <br />
                </h2>
              ))}
          </h2>
          <p className="ml-2 whitespace-pre-line lg:ml-40 lg:whitespace-normal xl:mt-5 xl:pl-[130px] xl:text-[30px]">
            {dictionary.home.feature2.desc || (
              <>
                <br />
                <br />
              </>
            )}
          </p>

          <div ref={sentinelRef} className="h-px w-px lg:block" />
          <motion.div
            initial={defaultAnimateConfig['SlideToRight3'].initial}
            animate={controls}
            transition={defaultAnimateConfig['SlideToRight3'].transition}
            className="relative"
          >
            <Image
              src={InsightImage}
              alt={'insight-image'}
              className="absolute -top-80 -right-38 w-[100%] lg:top-14 lg:left-43 lg:w-full lg:-translate-x-2/5 lg:scale-120 lg:rounded-[200px] xl:top-35 xl:-translate-x-[40%]"
            />
          </motion.div>
        </div>

        <div className="-mt-8 grid w-full gap-6 lg:mt-5 lg:w-5/7 xl:mt-23 xl:w-5/9 xl:self-start">
          <div className="grid gap-8 md:grid-cols-2 md:gap-10 lg:gap-x-10 lg:gap-y-5 lg:px-15 xl:gap-20 xl:px-23">
            {features.map((feature, i) => (
              <div key={i} className="mb-5">
                <div className="flex items-center justify-start gap-5 text-[24px] leading-[30px] font-[500] lg:text-[1.5vw] lg:leading-[20px] xl:min-h-[70px] xl:leading-[35px]">
                  <div className="w-[10dvw] lg:w-[3.5vw]">
                    <Image src={feature.icon} alt={feature.title} />
                  </div>
                  <span className="items-start whitespace-pre-line">
                    {feature.title}
                  </span>
                </div>
                <hr className="my-5 lg:my-4 xl:my-3" />
                <p className="text-[18px] font-[400] lg:text-[14px] xl:text-[18px]">
                  {feature.desc}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </TriggerAnimation>
  );
}

export default Feature;
