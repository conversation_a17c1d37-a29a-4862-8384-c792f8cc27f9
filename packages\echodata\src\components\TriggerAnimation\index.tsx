'use client';

import type { ReactNode } from 'react';
import { Children, cloneElement, useEffect, useRef } from 'react';

export default function TriggerAnimation({
  children,
}: {
  children: ReactNode;
}) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!ref.current) return;

    const handleScroll = () => {
      if (!ref.current) return;
      const rect = ref.current.getBoundingClientRect();
      // Trigger when the element's center is within the viewport
      const elementCenter = rect.top + rect.height / 2;
      if (
        elementCenter > window.innerHeight / 2 - rect.height / 2 &&
        elementCenter < window.innerHeight / 2 + rect.height / 2
      ) {
        ref.current.removeAttribute('data-trigger-animation');
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return Children.map(children, (child) =>
    typeof child === 'object' && child !== null && 'props' in child
      ? // eslint-disable-next-line @typescript-eslint/no-explicit-any
        cloneElement(child as any, {
          ref,
          'data-trigger-animation': true,
        })
      : child,
  );
}
