import PageWrapper from '@hi7/components/PageWrapper';
import type { Dictionary } from '@hi7/interface/dictionary';
import type { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary } from '@hi7/lib/i18n';
import Feature from './Feature';
import GetStarted from './GetStarted';
import Hero from './Hero';
import MainHighlight from './MainHighlight';
import WhyChooseUs from './WhyChooseUs';

async function page({ params }: LocaleProps) {
  const { locale } = params;
  const dictionary: Dictionary = await getDictionary(locale);

  const sectionConfigs = [
    { scrollDirection: 'default' as const },
    { scrollDirection: 'down' as const },
    { scrollDirection: 'default' as const },
    { scrollDirection: 'default' as const },
    { scrollDirection: 'default' as const, scrollFull: true, scrollSkip: true },
    { scrollDirection: 'default' as const },
  ];

  const ScrollBgGradient = () => (
    <div className="hidden h-[50vh] w-full bg-[#04227D] lg:block"></div>
  );

  return (
    <PageWrapper sectionConfigs={sectionConfigs}>
      <Hero dictionary={dictionary} />
      <Feature dictionary={dictionary} locale={locale} />
      <WhyChooseUs dictionary={dictionary} locale={locale} />
      <MainHighlight dictionary={dictionary} locale={locale} />
      <ScrollBgGradient />
      <GetStarted dictionary={dictionary} />
    </PageWrapper>
  );
}

export default page;
