import Bubbles from '@hi7/assets/background/bubble-element.svg';
import AnimationFrame from '../AnimationFrame';

function BubbleElement() {
  return (
    <section className="hidden h-screen w-screen overflow-hidden pt-15 lg:block lg:translate-y-[-8%] xl:translate-y-[0%]">
      <AnimationFrame
        variant="SlideInRight45Degree2"
        once={false}
        amount={0.1}
        className="h-screen w-full overflow-visible lg:w-[150vw]"
      >
        <Bubbles className="ml-[280px] -rotate-45 lg:scale-100 xl:mt-28 xl:ml-[500px] xl:scale-135" />
      </AnimationFrame>
    </section>
  );
}

export default BubbleElement;
