import type { Target, Transition } from 'motion/react';

export type VariantType =
  | 'None'
  | 'SlideIn'
  | 'SlideOut'
  | 'SlideUp'
  | 'FadeIn'
  | 'SlideDown'
  | 'SlideDownDelay'
  | 'RotateIn'
  | 'SlideInHigh'
  | 'SlideUp45AtEase'
  | 'SlideUp45AtEase2'
  | 'SlideUp45RightAtEase'
  | 'SlideTopRightEase'
  | 'SlideToRight'
  | 'SlideToRight2'
  | 'SlideToRight3'
  | 'SlideInRight45Degree'
  | 'SlideInRight45Degree2'
  | 'SlideInRight45DegreeFurther'
  | 'SlideInRight45DegreeFurther2'
  | 'ExpandWidth';

type MotionConfig = {
  initial: Target;
  animate: Target;
  transition: Transition;
};

export const defaultAnimateConfig: Record<VariantType, MotionConfig> = {
  None: {
    initial: { opacity: 1, scaleX: 1 },
    animate: { opacity: 1, scaleX: 1 },
    transition: { duration: 0 },
  },
  SlideIn: {
    initial: { x: 100, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    transition: { duration: 0.8, delay: 0.1 },
  },
  SlideOut: {
    initial: { x: -100, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    transition: { duration: 0.8, delay: 0.1 },
  },
  SlideUp: {
    initial: { y: 100, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    transition: { duration: 0.8, delay: 0.1 },
  },
  SlideDown: {
    initial: { y: -450, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    transition: { duration: 1.2, delay: 0.1 },
  },
  SlideDownDelay: {
    initial: { y: -250, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    transition: { duration: 1.2, delay: 0.5 },
  },

  FadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: 0.7, delay: 0.5 },
  },

  RotateIn: {
    initial: { rotate: -50, opacity: 0 },
    animate: { rotate: 0, opacity: 1 },
    transition: { duration: 0.5, ease: 'easeInOut' },
  },

  SlideInHigh: {
    initial: { y: 110, opacity: 0 },
    animate: { y: 1, opacity: 1 },
    transition: { duration: 0.8, delay: 0.8 },
  },

  SlideUp45AtEase: {
    initial: { x: '-40%', y: '-40%', opacity: 0 },
    animate: { x: 0, y: -260, opacity: 1 },
    transition: { duration: 1, ease: 'easeOut' },
  },

  SlideUp45AtEase2: {
    initial: { x: -220, y: 220, opacity: 0 },
    animate: { x: -1, y: 1, opacity: 1 },
    transition: { duration: 0.5, delay: 0.8 },
  },

  SlideUp45RightAtEase: {
    initial: { x: 0, y: -260, opacity: 1 },
    animate: { x: '60vw', y: '-90vw', opacity: 0 },
    transition: { duration: 0.7, ease: 'easeInOut' },
  },

  SlideToRight: {
    initial: { x: '-30vw', opacity: 1 },
    animate: { x: 0, opacity: 1 },
    transition: { duration: 0.8, delay: 0.2 },
  },

  SlideToRight2: {
    initial: { x: -500, opacity: 1 },
    animate: { x: 1, opacity: 1 },
    transition: { duration: 0.4, delay: 0.7 },
  },

  SlideToRight3: {
    initial: { x: -500, opacity: 0 },
    animate: { x: 1, opacity: 1 },
    transition: { duration: 0.4, delay: 0.77 },
  },

  SlideInRight45Degree: {
    initial: { x: '50vw', y: '50vh', opacity: 0 },
    animate: { x: 0, y: 0, opacity: 1 },
    transition: { duration: 0.8, delay: 0.3, ease: 'easeOut' },
  },

  SlideInRight45Degree2: {
    initial: { x: '50vw', y: '50vh', opacity: 1 },
    animate: { x: '-100vw', y: '-100vh', opacity: 1 },
    transition: { duration: 0.8, delay: 0, ease: 'linear' },
  },

  SlideInRight45DegreeFurther: {
    initial: { x: 0, y: 0, opacity: 1 },
    animate: { x: '-50vw', y: '-50vh', opacity: 0 },
    transition: { duration: 0.8, ease: 'easeInOut' },
  },

  SlideInRight45DegreeFurther2: {
    initial: { x: '10vw', y: '10vh', opacity: 0 },
    animate: { x: '-100%', y: '-100%', opacity: 1 },
    transition: { duration: 1.5, delay: 0, ease: 'easeOut' },
  },

  ExpandWidth: {
    initial: { marginLeft: '-5rem', marginRight: '-5rem', opacity: 1 },
    animate: { marginLeft: '0rem', marginRight: '0rem', opacity: 1 },
    transition: { duration: 1 },
  },

  SlideTopRightEase: {
    initial: { x: '-40vw', y: '40vw', opacity: 1 },
    animate: { x: 0, y: 0, opacity: 1 },
    transition: { duration: 0.8, delay: 0.5, ease: 'easeOut' },
  },
};
