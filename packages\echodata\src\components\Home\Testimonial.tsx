'use client';

import BluePolygonBgMobile from '@hi7/assets/background/polygon-vector-blue-mobile.png';
import BluePolygonBg from '@hi7/assets/background/polygon-vector-blue.png';
import PinkPolygonBgMobile from '@hi7/assets/background/polygon-vector-pink-mobile.png';
import PinkPolygonBg from '@hi7/assets/background/polygon-vector-pink.png';
import type { DictionaryProps } from '@hi7/interface/i18n';
import clsx from 'clsx';
import AutoScroll from 'embla-carousel-auto-scroll';
import useEmblaCarousel from 'embla-carousel-react';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';
import TriggerAnimation from '../TriggerAnimation';
import type { ClientKey } from './config';
import { FEEDBACKS } from './config';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
});

function Feedback({ dictionary }: DictionaryProps) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: false,
    align: 'center',
    containScroll: 'trimSnaps',
    skipSnaps: false,
    dragFree: false,
    slidesToScroll: 1,
    duration: 25,
  });
  const [desktopEmblaRef, desktopEmblaApi] = useEmblaCarousel(
    {
      loop: true,
      align: 'start',
      dragFree: true,
      containScroll: false,
      skipSnaps: false,
      inViewThreshold: 0.7,
    },
    [
      AutoScroll({
        playOnInit: true,
        speed: 1,
        stopOnInteraction: true,
        stopOnMouseEnter: true,
      }),
    ],
  );
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    emblaApi.on('select', onSelect);
    onSelect();
    return () => {
      emblaApi.off('select', onSelect);
    };
  }, [emblaApi, onSelect]);

  const scrollTo = useCallback(
    (index: number) => {
      if (!emblaApi) return;
      emblaApi.scrollTo(index);
    },
    [emblaApi],
  );

  // Handle drag events and control auto-scroll plugin
  useEffect(() => {
    if (!desktopEmblaApi) return;
    const autoScroll = desktopEmblaApi.plugins()?.autoScroll as
      | { play: () => void; stop: () => void }
      | undefined;

    let dragTimeout: NodeJS.Timeout;

    const onPointerDown = () => {
      clearTimeout(dragTimeout);
      autoScroll?.stop();
      setIsAutoPlaying(false);
    };

    const onPointerUp = () => {
      clearTimeout(dragTimeout);
      dragTimeout = setTimeout(() => {
        setIsAutoPlaying(true);
        autoScroll?.play();
      }, 200);
    };

    desktopEmblaApi.on('pointerDown', onPointerDown);
    desktopEmblaApi.on('pointerUp', onPointerUp);

    return () => {
      clearTimeout(dragTimeout);
      desktopEmblaApi.off('pointerDown', onPointerDown);
      desktopEmblaApi.off('pointerUp', onPointerUp);
    };
  }, [desktopEmblaApi]);

  useEffect(() => {
    if (!desktopEmblaApi) return;
    const root = desktopEmblaApi.rootNode() as HTMLElement | null;
    const autoScroll = desktopEmblaApi.plugins()?.autoScroll as
      | { play: () => void; stop: () => void }
      | undefined;

    if (!root) return;

    const onMouseEnter = () => {
      autoScroll?.stop();
      setIsAutoPlaying(false);
    };
    const onMouseLeave = () => {
      setIsAutoPlaying(true);

      setTimeout(() => autoScroll?.play(), 200);
    };

    root.addEventListener('mouseenter', onMouseEnter);
    root.addEventListener('mouseleave', onMouseLeave);

    return () => {
      root.removeEventListener('mouseenter', onMouseEnter);
      root.removeEventListener('mouseleave', onMouseLeave);
    };
  }, [desktopEmblaApi]);

  return (
    <TriggerAnimation>
      <div className="w-full">
        <div className="flex flex-col items-start justify-center px-20 pt-[50px] md:py-[70px] lg:mt-[110px] lg:pt-[100px] xl:py-[120px]">
          <h2
            className={`text-[40px] font-bold text-[#04227D] lg:text-[45px] xl:ml-20 xl:text-[60px] ${arsenal.className}`}
          >
            {dictionary.home.client.title}
          </h2>
        </div>
      </div>

      <div className="-mt-5 lg:-mt-10">
        <div
          ref={emblaRef}
          className="h-[680px] touch-pan-y overflow-x-hidden overflow-y-hidden md:hidden"
        >
          {/******* Mobile view ******/}
          <div className="embla__container flex gap-x-15 px-6">
            <div className="w-[80vw] max-w-[320px] flex-none snap-start">
              <div className="grid grid-rows-2 gap-y-4">
                {FEEDBACKS.slice(0, 2).map(({ key }, index) => {
                  const { name, role, desc } =
                    dictionary.home.client[key as ClientKey];
                  return (
                    <div
                      key={index}
                      className={clsx(index % 2 !== 0 ? '-mt-[70px]' : 'mt-0')}
                    >
                      <div
                        className="relative content-evenly overflow-hidden"
                        style={{ aspectRatio: '4 / 5' }}
                      >
                        <Image
                          fill
                          src={
                            index % 2 === 0
                              ? PinkPolygonBgMobile
                              : BluePolygonBgMobile
                          }
                          alt={''}
                          className="w-full object-contain"
                        />
                        <div
                          className={`relative flex h-full flex-col items-start px-6 py-10 ${index % 2 === 0 ? 'text-[#FF5542]' : 'text-[#04227D]'}`}
                        >
                          <div className="-mb-15 flex flex-1 items-center justify-center">
                            <p className="text-center text-start text-[15px] font-[300]">
                              {desc}
                            </p>
                          </div>
                          <div className="mt-auto mb-10 flex items-center gap-4 text-sm">
                            <b>{name}</b>
                            <p>{role}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            <div className="w-[80vw] max-w-[320px] flex-none snap-start">
              <div className="grid grid-rows-2 gap-y-4">
                {FEEDBACKS.slice(2, 4).map(({ key }, index) => {
                  const actualIndex = index + 2;
                  const { name, role, desc } =
                    dictionary.home.client[key as ClientKey];
                  return (
                    <div
                      key={actualIndex}
                      className={clsx(
                        actualIndex % 2 !== 0 ? '-mt-[70px]' : 'mt-0',
                      )}
                    >
                      <div
                        className="relative content-evenly overflow-hidden"
                        style={{ aspectRatio: '4 / 5' }}
                      >
                        <Image
                          fill
                          src={
                            actualIndex % 2 === 0
                              ? PinkPolygonBgMobile
                              : BluePolygonBgMobile
                          }
                          alt={''}
                          className="w-full object-contain"
                        />
                        <div
                          className={`relative flex h-full flex-col items-start px-6 py-10 ${actualIndex % 2 === 0 ? 'text-[#FF5542]' : 'text-[#04227D]'}`}
                        >
                          <div className="-mb-15 flex flex-1 items-center justify-center">
                            <p className="text-center text-start text-[15px] font-[300]">
                              {desc}
                            </p>
                          </div>
                          <div className="mt-auto mb-10 flex items-center gap-4 text-sm">
                            <b>{name}</b>
                            <p>{role}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            <div className="w-[80vw] max-w-[320px] flex-none snap-start">
              <div className="grid grid-rows-2 gap-y-4">
                {FEEDBACKS.slice(4, 6).map(({ key }, index) => {
                  const actualIndex = index + 4;
                  const { name, role, desc } =
                    dictionary.home.client[key as ClientKey];
                  return (
                    <div
                      key={actualIndex}
                      className={clsx(
                        actualIndex % 2 !== 0 ? '-mt-[70px]' : 'mt-0',
                      )}
                    >
                      <div
                        className="relative content-evenly overflow-hidden"
                        style={{ aspectRatio: '4 / 5' }}
                      >
                        <Image
                          fill
                          src={
                            actualIndex % 2 === 0
                              ? PinkPolygonBgMobile
                              : BluePolygonBgMobile
                          }
                          alt={''}
                          className="w-full object-contain"
                        />
                        <div
                          className={`relative flex h-full flex-col items-start px-6 py-10 ${actualIndex % 2 === 0 ? 'text-[#FF5542]' : 'text-[#04227D]'}`}
                        >
                          <div className="-mb-15 flex flex-1 items-center justify-center">
                            <p className="text-center text-start text-[15px] font-[300]">
                              {desc}
                            </p>
                          </div>
                          <div className="mt-auto mb-10 flex items-center gap-4 text-sm">
                            <b>{name}</b>
                            <p>{role}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/******* Desktop view ******/}
        <div className="relative z-10 hidden cursor-grab justify-center select-none md:flex">
          <div className="absolute top-[0%] left-0 opacity-100">
            <div className="h-[70vh] w-[30vw] bg-linear-[90deg,#fff_30%,#ffffff00_100%]" />
          </div>
          <div className="absolute top-0 right-0 opacity-100">
            <div className="h-[70vh] w-[30vw] bg-linear-[270deg,#fff_30%,#ffffff00_100%]" />
          </div>
        </div>
        {/* Desktop view with CSS animation + drag functionality */}
        <div className="mx-auto hidden h-[70vh] max-w-[1600px] overflow-hidden md:block xl:h-[73vh]">
          <div
            ref={desktopEmblaRef}
            className={`embla__viewport h-full ${!isAutoPlaying ? 'cursor-grabbing' : 'cursor-grab'}`}
          >
            <div className={`embla__container flex gap-6 px-10`}>
              {/* First set of testimonials */}
              {FEEDBACKS.map(({ key }, index) => {
                const { name, role, desc } =
                  dictionary.home.client[key as ClientKey];

                return (
                  <div
                    key={`first-${index}`}
                    className="embla__slide w-[320px] flex-none select-none xl:w-[420px]"
                  >
                    <div
                      className="relative mr-8 overflow-hidden xl:mr-10"
                      style={{ aspectRatio: '4 / 5' }}
                    >
                      <Image
                        fill
                        src={index % 2 === 0 ? PinkPolygonBg : BluePolygonBg}
                        alt="polygon background"
                        className="w-full object-contain"
                      />
                      <div
                        className={`relative z-10 flex h-full flex-col items-start px-6 py-10 xl:px-8 ${index % 2 === 0 ? 'text-[#FF5542]' : 'text-[#04227D]'}`}
                      >
                        <div className="flex flex-1 items-center justify-start">
                          <p className="text-[22px] font-[300] md:text-[18px] lg:text-[18px] xl:text-[24px] xl:font-thin">
                            {desc}
                          </p>
                        </div>
                        <div className="xl:text-md mt-auto flex items-center gap-1 text-start text-xs">
                          <b>{name}</b>
                          <p>{role}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        <div className="mt-4 flex justify-center gap-2 md:hidden">
          {[0, 1, 2].map((columnIndex) => (
            <button
              key={columnIndex}
              onClick={() => scrollTo(columnIndex)}
              className={`h-2 w-2 rounded-full ${
                selectedIndex === columnIndex ? 'bg-blue-500' : 'bg-gray-300'
              }`}
            />
          ))}
        </div>
      </div>
    </TriggerAnimation>
  );
}

export default Feedback;
