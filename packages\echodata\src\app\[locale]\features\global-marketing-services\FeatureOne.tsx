import SlidingPolygon from '@hi7/assets/background/global-market-vector-bg.png';
import MeetingImg from '@hi7/assets/background/meeting-background.jpg';
import Point1 from '@hi7/assets/icon/api-screen.svg';
import Point2 from '@hi7/assets/icon/feature8.svg';
import AnimationFrame from '@hi7/components/AnimationFrame';
import type { DictionaryProps } from '@hi7/interface/i18n';
import clsx from 'clsx';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

function AppIntergration({ dictionary }: DictionaryProps) {
  const points = [
    {
      icon: Point1,
      title:
        dictionary.features.globalMarketingServices.section1.apiInterface.title,
      desc: dictionary.features.globalMarketingServices.section1.apiInterface
        .desc,
    },
    {
      icon: Point2,
      title:
        dictionary.features.globalMarketingServices.section1
          .filteringAndMarketing.title,
      desc: dictionary.features.globalMarketingServices.section1
        .filteringAndMarketing.desc,
    },
  ];

  return (
    <section className="relative -mt-30 h-auto md:mt-40 lg:mt-10 lg:mb-10">
      <div className="lg:hidden">
        <div className="flex h-auto flex-row md:flex-row lg:h-[130px]">
          <div
            className={clsx(
              'z-10 order-2 -mb-5 flex w-full items-end justify-end rounded-t-[40px] bg-[#E9F3FF] pr-6',
              'md:order-1 md:-mb-10 md:mb-0 md:w-1/3 md:pr-10',
              'lg:-mb-27 lg:w-4/9 lg:items-end lg:justify-start lg:rounded-t-[70px] lg:pr-0 lg:pl-16',
              'xl:pl-20',
            )}
          >
            <h2
              className={`${arsenal.className} text-right text-[40px] leading-[42px] font-bold text-[#FF5542] md:text-[45px] lg:text-left lg:text-[64px] lg:leading-[72px]`}
              dangerouslySetInnerHTML={{
                __html:
                  dictionary.features.globalMarketingServices.section1.title,
              }}
            />
          </div>

          <div className="relative order-1 w-full md:order-2 md:w-2/3 lg:w-5/9">
            <Image
              src={MeetingImg}
              alt={'insight-img'}
              className={clsx(
                '-mt-28 h-[30vh] w-full rounded-r-[45px] border-t-25 border-r-25 border-b-25 border-white object-cover',
                'md:mr-[140px] md:rounded-[70px] md:rounded-r-none md:border-r-0 md:border-l-25',
                'lg:mx-50 lg:-ml-0 lg:h-[50vh] lg:translate-y-[-5%] lg:rounded-[85px] lg:border-r-[45px] lg:border-b-30 lg:border-l-35',
                'xl:h-[45vh] xl:translate-y-[-20%]',
              )}
            />
          </div>
        </div>

        <div className="-mt-[50px] rounded-br-[40px] bg-[#E9F3FF] pt-20 pb-10 lg:mt-0 lg:rounded-tr-[150px] lg:rounded-b-[60px] lg:pt-30">
          <hr className="mx-auto w-[90%] border-[#FF5542] xl:mt-8 xl:w-[92%]" />
          <div className="mt-6 grid grid-cols-1 gap-0 md:grid-cols-2 lg:mt-0 lg:grid-cols-3 lg:px-6 lg:pb-6 xl:mt-5 xl:gap-13">
            {points.length > 0 &&
              points.map((point, index) => (
                <div
                  key={index}
                  className="mb-0 flex w-full flex-col gap-4 rounded-[30px] bg-[#E9F3FF] p-7 lg:px-10 lg:py-8 xl:px-20"
                >
                  <div className="flex flex-row items-center justify-start text-[#FF5542] xl:justify-start">
                    <point.icon className="w-[28%] xl:w-[20%]" />
                    <h1 className="pl-3 text-[24px] leading-[25px] font-[600] whitespace-pre-line lg:text-[24px] xl:text-[29px]">
                      {point.title}
                    </h1>
                  </div>
                  <hr />
                  <span className="text-[18px] font-[300] text-[#04227D] xl:text-[25px]">
                    {point.desc}
                  </span>
                </div>
              ))}
          </div>
        </div>
      </div>

      <div className="hidden items-center justify-center lg:flex lg:flex-row">
        <AnimationFrame
          variant="SlideToRight"
          once={false}
          className="relative w-full"
        >
          <div className="w-5/7 translate-x-[-8%] content-center">
            <Image
              src={MeetingImg}
              alt={'insight-img'}
              className={clsx('h-[80vh] w-full rounded-[50px] object-cover')}
            />
          </div>
          <div className="relative mt-10 h-full w-full">
            <Image
              src={SlidingPolygon}
              alt="background"
              className="h-full w-full scale-y-115 object-cover xl:scale-y-100"
            />

            <AnimationFrame
              variant="FadeIn"
              once={false}
              className="absolute top-7 left-0 w-full max-w-[700px] px-8 lg:px-16 xl:top-30 xl:left-15"
            >
              <div className="">
                <h1
                  className={`${arsenal.className} text-right text-[40px] leading-[36px] font-bold text-[#FF5542] md:text-[45px] lg:text-left lg:text-[64px] lg:leading-[65px]`}
                  dangerouslySetInnerHTML={{
                    __html:
                      dictionary.features.globalMarketingServices.section1
                        .title,
                  }}
                />
                <hr className="my-5 w-[55%] border-[#FF5542] xl:w-[90%]" />
                <div className="mt-8 mb-4 flex w-full flex-col xl:mt-20">
                  {points.length > 0 &&
                    points.map((point, index) => (
                      <div className="mb-10 xl:mb-20" key={index}>
                        <div className="flex flex-row items-center justify-start text-[#FF5542]">
                          <point.icon className="w-[12%] xl:w-[20%]" />
                          <h1 className="w-[45%] text-[24px] leading-[25px] font-[500] lg:text-[24px] xl:w-[60%] xl:text-[29px] xl:leading-[32px]">
                            {point.title}
                          </h1>
                        </div>
                        <hr className="my-3 w-[55%] xl:w-[90%]" />
                        <p className="w-[55%] font-[400] text-[#04227D] xl:w-[90%] xl:text-[20px]">
                          {point.desc}
                        </p>
                      </div>
                    ))}
                </div>
              </div>
            </AnimationFrame>
          </div>
        </AnimationFrame>
      </div>
    </section>
  );
}

export default AppIntergration;
