import BubbleTrain from '@hi7/assets/background/bubble-train.svg';
import QuestionMark from '@hi7/assets/icon/question-mark.svg';
import Reason1 from '@hi7/assets/icon/reason1.svg?url';
import Reason2 from '@hi7/assets/icon/reason2.svg?url';
import Reason3 from '@hi7/assets/icon/reason3.svg?url';
import Reason4 from '@hi7/assets/icon/reason4.svg?url';
import Reason5 from '@hi7/assets/icon/reason5.svg?url';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';
import AnimationFrame from '../AnimationFrame';
import TriggerAnimation from '../TriggerAnimation';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
});

function WhyChooseUs({ dictionary }: DictionaryProps) {
  const reasons = [
    {
      icon: Reason1,
      title: dictionary.home.whychooseus.marketingCost.title,
      desc: dictionary.home.whychooseus.marketingCost.desc,
    },
    {
      icon: Reason2,
      title: dictionary.home.whychooseus.highQuality.title,
      desc: dictionary.home.whychooseus.highQuality.desc,
    },
    {
      icon: Reason3,
      title: dictionary.home.whychooseus.businessOpportunities.title,
      desc: dictionary.home.whychooseus.businessOpportunities.desc,
    },
    {
      icon: Reason4,
      title: dictionary.home.whychooseus.closeDeals.title,
      desc: dictionary.home.whychooseus.closeDeals.desc,
    },
    {
      icon: Reason5,
      title: dictionary.home.whychooseus.userExperience.title,
      desc: dictionary.home.whychooseus.userExperience.desc,
    },
  ];

  return (
    <TriggerAnimation>
      <div className="flex items-center justify-center overflow-x-clip rounded-[50px] bg-[#E9F3FF] text-black lg:mt-10 lg:h-screen lg:rounded-l-[120px] lg:rounded-r-[0px] lg:py-25 xl:mt-25 xl:rounded-l-[150px]">
        <div className="w-full overflow-x-clip">
          <div className="relative lg:min-h-[560px]">
            <div className="flex flex-col items-center justify-center px-10 py-8 pt-12 pb-12 lg:items-start lg:px-20 lg:pt-18 lg:pb-[40px] xl:px-30 xl:pt-0">
              <h2
                className={`${arsenal.className} text-left text-[37px] leading-[45px] font-bold text-red-400 lg:text-center lg:text-[48px] lg:leading-[58px] xl:pb-6 xl:text-[70px]`}
              >
                {dictionary.home.whychooseus.title}
              </h2>
            </div>
            <div className="grid items-start justify-start gap-y-[48px] px-10 text-[#047AFF] lg:grid-cols-[250px_250px_250px] lg:gap-x-[50px] lg:gap-y-[15px] lg:px-20 xl:grid-cols-[360px_368px_380px] xl:justify-start xl:gap-x-[60px] xl:pl-30">
              {reasons.length > 0 &&
                reasons.map((reason, index) => (
                  <div
                    key={index}
                    className="lg:animate-home-help-0 z-1 flex flex-col items-start justify-center gap-2 lg:translate-x-[-100%] lg:opacity-0"
                  >
                    <div className="w-[10dvw] lg:w-[2.5vw] xl:w-[3vw]">
                      <Image src={reason.icon} alt={reason.title} />
                    </div>
                    <h2 className="mt-1 text-[24px] font-medium lg:text-[1.5dvw] xl:text-[24px]">
                      {reason.title}
                    </h2>
                    <hr className="w-full" />
                    <p className="mt-1 text-[18px] font-light lg:text-[1dvw] xl:text-[18px]">
                      {reason.desc}
                    </p>
                  </div>
                ))}
            </div>
            <AnimationFrame
              variant="RotateIn"
              once={false}
              className="z-0 -mb-10 ml-10 hidden h-full w-full items-center justify-end md:flex lg:absolute lg:top-5 lg:-right-40 xl:top-10 xl:-right-50"
            >
              <QuestionMark className="h-65 w-65 fill-current text-black lg:h-120 lg:w-120 xl:h-200 xl:w-200" />
            </AnimationFrame>

            <div className="z-0 -mb-10 ml-10 flex h-full w-full items-center justify-end md:hidden">
              <QuestionMark className="h-65 w-65 fill-current text-black lg:h-150 lg:w-150 xl:h-200 xl:w-200" />
            </div>
          </div>
          <div className="relative overflow-x-clip">
            <BubbleTrain className="animate-infinite-slide-mobile lg:animate-infinite-slide absolute -left-150 z-[-1] scale-30 rotate-45 fill-current lg:-left-1/2 lg:scale-75 xl:-left-1/3 xl:scale-85" />
          </div>
        </div>
      </div>
    </TriggerAnimation>
  );
}

export default WhyChooseUs;
