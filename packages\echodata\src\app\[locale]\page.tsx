import AppIntergration from '@hi7/components/Home/AppIntegration';
import BubbleElement from '@hi7/components/Home/BubbleElement';
import FeatureOne from '@hi7/components/Home/FeatureOne';
import FeatureTwo from '@hi7/components/Home/FeatureTwo';
import GetStarted from '@hi7/components/Home/GetStarted';
import Landing from '@hi7/components/Home/Landing';
import Testimonial from '@hi7/components/Home/Testimonial';
import WhyChooseUs from '@hi7/components/Home/WhyChooseUs';
import PageWrapper from '@hi7/components/PageWrapper';
import type { Dictionary } from '@hi7/interface/dictionary';
import { getDictionary, type Locale } from '@hi7/lib/i18n';

type PageProps = {
  params: {
    locale: Locale;
  };
};

export default async function Page({ params }: PageProps) {
  const { locale } = params;
  const t: Dictionary = await getDictionary(locale);

  const sectionConfigs = [
    { scrollDirection: 'default' as const },
    {
      scrollDirection: 'default' as const,
      scrollSkip: true,
      scrollSkipDuration: 500,
    },
    { scrollDirection: 'default' as const, scrollFull: true },
    { scrollDirection: 'default' as const, scrollFull: true },
    { scrollDirection: 'down' as const },
    { scrollDirection: 'default' as const },
    { scrollDirection: 'default' as const, scrollFull: true },
    { scrollDirection: 'default' as const, scrollFull: true, scrollSkip: true },
    { scrollDirection: 'default' as const },
  ];

  const ScrollBgGradient = () => (
    <div className="h-[32vh] w-full bg-gradient-to-b from-[#ffffff] to-[#04227D] to-100% lg:h-[50vh]"></div>
  );

  return (
    <PageWrapper sectionConfigs={sectionConfigs}>
      <Landing dictionary={t} />
      <div className="overflow-hidden">
        <BubbleElement />
      </div>
      <AppIntergration dictionary={t} />
      <WhyChooseUs dictionary={t} />
      <FeatureOne dictionary={t} />
      <FeatureTwo dictionary={t} />
      <Testimonial dictionary={t} />
      <ScrollBgGradient />
      <GetStarted dictionary={t} />
    </PageWrapper>
  );
}
