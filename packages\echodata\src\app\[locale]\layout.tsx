import QueryClientProviders from '@hi7/provider/QueryClientRegistry';

import Fab from '@hi7/components/Fab';
import { getFeatureFlags } from '@hi7/helpers/featureFlag';
import { getBaseurl, getPathname } from '@hi7/helpers/pathname';
import type { Dictionary } from '@hi7/interface/dictionary';
import type { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary, type Locale } from '@hi7/lib/i18n';
import ProgressProvider from '@hi7/provider/ProgressProvider';
import ZustandContext from '@hi7/provider/ZustandContext';
import { GoogleAnalytics } from '@next/third-parties/google';
import { Heebo as HeeboFont, Noto_Sans_SC as Noto } from 'next/font/google';
import Head from 'next/head';
import Script from 'next/script';
import './globals.css';
import LayoutContent from './layout-content';

export const generateMetadata = async ({ params }: LocaleProps) => {
  const { locale } = params;
  const t = await getDictionary(locale);

  return {
    title: t.general.meta.title,
    description: t.general.meta.description,

    // keywords: t.general.meta.keywords,
  };
};

const enFont = HeeboFont({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
});

const zhFont = Noto({
  weight: ['400', '700'],
  subsets: ['latin'],
});

const FONTS = {
  en: enFont,
  zh: zhFont,
} satisfies Record<Locale, typeof enFont>;

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: { locale: Locale };
}>) {
  const { locale = 'en' } = params;
  const t: Dictionary = await getDictionary(locale);
  const [baseurl, pathname, featureFlag] = await Promise.all([
    getBaseurl(),
    getPathname(),
    getFeatureFlags(),
  ]);

  return (
    <html lang={locale} className={FONTS[locale].className}>
      {/* Google Tag Manager */}
      {process.env.HI7_GA_ALT_TAG && (
        <>
          <GoogleAnalytics gaId={process.env.HI7_GA_ALT_TAG} />
          <Script
            id="init-gtm-tag"
            dangerouslySetInnerHTML={{
              __html: `window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', '${process.env.HI7_GA_ALT_TAG}');`,
            }}
          />
        </>
      )}

      {/* Google Ads/Google Analytics  */}
      {process.env.HI7_GA_TAG && (
        <>
          <GoogleAnalytics gaId={process.env.HI7_GA_TAG} />
          <Script
            id="init-ga-tag"
            dangerouslySetInnerHTML={{
              __html: `window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', '${process.env.HI7_GA_TAG}');`,
            }}
          />
          <Script
            id="init-ga-page-view"
            dangerouslySetInnerHTML={{
              __html: `gtag('event', 'conversion', {'send_to': '${process.env.HI7_GA_TAG}/${process.env.HI7_GA_PAGE_VIEW_CONVERSION_LABEL}'});`,
            }}
          />
        </>
      )}

      {/* Meta/Facebook Pixel  */}
      {process.env.HI7_FB_TAG && (
        <>
          <Head>
            <noscript>
              {/* eslint-disable-next-line @next/next/no-img-element, jsx-a11y/alt-text */}
              <img
                height="1"
                width="1"
                style={{ display: 'none' }}
                src={`https://www.facebook.com/tr?id=${process.env.HI7_FB_TAG}&ev=PageView&noscript=1`}
              />
            </noscript>
          </Head>
          <Script id="facebook-pixel" strategy="afterInteractive">
            {`
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', ${process.env.HI7_FB_TAG});
    fbq('track', 'PageView');
  `}
          </Script>
        </>
      )}
      <body>
        <QueryClientProviders>
          <ZustandContext config={{ baseurl }} featureFlag={featureFlag}>
            <ProgressProvider>
              <LayoutContent locale={locale} dictionary={t} pathname={pathname}>
                {children}
                <Fab locale={locale} dictionary={t} />
              </LayoutContent>
            </ProgressProvider>
          </ZustandContext>
        </QueryClientProviders>
      </body>
    </html>
  );
}
