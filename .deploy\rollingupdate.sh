#!/bin/sh
REGION_ID="ap-southeast-1"
SCALING_GROUP_NAME="baymax-echodata-$ENV-ess"
SCALING_GROUP_ID=$(aliyun ess DescribeScalingGroups --RegionId $REGION_ID --ScalingGroupName "$SCALING_GROUP_NAME" | jq -r '.ScalingGroups.ScalingGroup[] | .ScalingGroupId')
SCALING_CONFIGURATION_NAME="baymax-echodata-$ENV-sc"
SCALING_CONFIGURATION_ID=$(aliyun ess DescribeEciScalingConfigurations --RegionId $REGION_ID --ScalingGroupId "$SCALING_GROUP_ID" | jq -r --arg SC_NAME "$SCALING_CONFIGURATION_NAME" '.ScalingConfigurations[] | select(.ScalingConfigurationName == $SC_NAME) | .ScalingConfigurationId')
echo "Region ID: $REGION_ID"
echo "Scaling Group Name: $SCALING_GROUP_NAME"
echo "Scaling Group ID: $SCALING_GROUP_ID"
echo "Scaling Configuration ID: $SCALING_CONFIGURATION_ID"

aliyun oos StartExecution \
  --region $REGION_ID \
  --RegionId ${REGION_ID} \
  --TemplateName 'ACS-ESS-RollingUpdateByUpdateContainerGroup' \
  --Parameters "{
    \"invokeType\":\"invoke\",
    \"scalingGroupId\":\"${SCALING_GROUP_ID}\",
    \"containerConfigure\":[
      {
        \"Memory\": 4,
        \"Cpu\": 2,
        \"Image\": \"hiseven-registry.ap-southeast-1.cr.aliyuncs.com/baymax/echodata:${TAG}\",
        \"Name\": \"baymax-echodata-${ENV}\",
        \"Volumes\": [],
        \"ImagePullPolicy\": \"IfNotPresent\",
        \"Command\": [],
        \"Arg\": [],
        \"VolumeMount\": [],
        \"EnvironmentVar\": [
          {
              \"Key\": \"ENVIRONMENT\",
              \"Value\": \"${ENV}\"
          },
          {
              \"Key\": \"PORT\",
              \"Value\": \"${PORT}\"
          },
          {
              \"Key\": \"SERVICE_NAME\",
              \"Value\": \"${SERVICE_NAME}\"
          }
        ]
      }
    ],
    \"sourceContainerConfigure\":[
      {
        \"Memory\": 4,
        \"Cpu\": 2,
        \"Image\": \"hiseven-registry.ap-southeast-1.cr.aliyuncs.com/baymax/echodata:${TAG}\",
        \"Name\": \"baymax-echodata-${ENV}\",
        \"Volumes\": [],
        \"ImagePullPolicy\": \"IfNotPresent\",
        \"Command\": [],
        \"Arg\": [],
        \"VolumeMount\": [],
        \"EnvironmentVar\": [
          {
              \"Key\": \"ENVIRONMENT\",
              \"Value\": \"${ENV}\"
          },
          {
              \"Key\": \"PORT\",
              \"Value\": \"${PORT}\"
          },
          {
              \"Key\": \"SERVICE_NAME\",
              \"Value\": \"${SERVICE_NAME}\"
          }
        ]
      }
    ],
    \"updateType\":\"IncrementalUpdate\",
    \"sourceExecutionId\":\"\",
    \"scalingConfigurationId\":\"${SCALING_CONFIGURATION_ID}\",
    \"batchPauseOption\":\"Automatic\",
    \"batchNumber\":1,
    \"maxErrors\":0,
    \"OOSAssumeRole\":\"\"
  }" \
  --Description '' \
  --Mode Automatic \
  --Tags "{\"Scaling Group\":\"${SCALING_GROUP_NAME}\",\"Env\":\"${ENV}\"}"
