'use client';

import ContactUsButton from '@hi7/assets/icon/contact-us-button.svg';
import ContactUsClose from '@hi7/assets/icon/contact-us-close.svg';
import ContactUsTextZh from '@hi7/assets/icon/contact-us-text-zh.svg';
import ContactUsText from '@hi7/assets/icon/contact-us-text.svg';
import ContactUsTG from '@hi7/assets/icon/contact-us-tg.svg';
import ContactUsWS from '@hi7/assets/icon/contact-us-ws.png';
import LatestNews from '@hi7/assets/icon/latest-news.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import clsx from 'clsx';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import Link from '../Link';

function Fab({ dictionary, locale }: DictionaryProps) {
  const pathname = usePathname();

  const ContactUsTextComponent =
    locale === 'zh' ? ContactUsTextZh : ContactUsText;

  const [isLatestOpen, setIsLatestOpen] = useState(false);
  const clearLatestTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  function handleLatestMouseEnter() {
    clearLatestTimeoutRef.current &&
      clearTimeout(clearLatestTimeoutRef.current);
    setIsLatestOpen(true);
  }

  function handleLatestMouseLeave() {
    clearLatestTimeoutRef.current &&
      clearTimeout(clearLatestTimeoutRef.current);
    clearLatestTimeoutRef.current = setTimeout(() => {
      setIsLatestOpen(false);
    }, 500);
  }

  const [isContactOpen, setIsContactOpen] = useState(false);
  const contactRef = useRef<HTMLDivElement>(null);

  function handleContactClick() {
    setIsContactOpen(!isContactOpen);
  }

  function handleClickOutside(event: MouseEvent) {
    if (
      contactRef.current &&
      !contactRef.current.contains(event.target as Node)
    ) {
      setIsContactOpen(false);
    }
  }

  useEffect(() => {
    if (isContactOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isContactOpen]);

  // Hide Fab on industry insights detail pages
  const shouldHideFab =
    pathname.includes('/industry-insights/') && pathname.split('/').length > 3; // Excludes main industry-insights page

  if (shouldHideFab) {
    return null;
  }

  return (
    <>
      <div
        ref={contactRef}
        className="fixed right-0 bottom-[20%] z-50 lg:right-[5%]"
        onClick={handleContactClick}
      >
        {isContactOpen && (
          <div className="animate-fab-latest-contact absolute right-0 -bottom-[47px] select-none lg:-right-16 xl:-right-22">
            <div
              className={clsx(
                locale === 'zh'
                  ? 'mb-[-20px] ml-[25px]'
                  : 'mb-[-30px] ml-[10px]',
              )}
            >
              <ContactUsTextComponent />
            </div>
            <a
              className="mb-[-45px] block"
              href="https://007tg.com/ccs/champions"
              target="_blank"
              rel="noopener noreferrer"
            >
              <ContactUsTG />
            </a>

            <a
              className="relative mb-[-45px] block h-[110px] w-[110px]"
              href={dictionary.footer.customerService.url}
              target="_blank"
              rel="noopener noreferrer"
            >
              <Image src={ContactUsWS} alt="Contact Us WhatsApp" fill />
            </a>
            <ContactUsClose />
          </div>
        )}

        <div
          className={clsx(
            'absolute',
            isContactOpen && 'animate-fab-latest-contact-reserve',
          )}
        >
          {!isContactOpen && (
            <div
              className={clsx(
                'absolute right-4 bottom-4 z-50',
                locale === 'zh'
                  ? 'lg:-top-11 lg:-right-9 xl:-right-15'
                  : 'lg:-right-12 xl:-right-18',
              )}
            >
              <ContactUsTextComponent />
            </div>
          )}

          <div className="absolute right-8 -bottom-7 z-50 lg:-right-8 xl:-right-14">
            <ContactUsButton className="scale-110" />
          </div>
        </div>
      </div>

      <Link
        url=""
        className={clsx(
          'fixed right-8 bottom-[10%] z-50 h-[48px] overflow-hidden rounded-full lg:right-[2.5%] lg:bottom-[5%] xl:right-[2%]',
          isLatestOpen ? 'w-[165px]' : 'w-[48px]',
        )}
        onMouseEnter={handleLatestMouseEnter}
        onMouseLeave={handleLatestMouseLeave}
      >
        {/* The LatestNews icon container will now be absolutely positioned to the right */}
        <div className="absolute top-0 right-0 z-50 flex h-[48px] w-[48px] scale-110 items-center justify-center rounded-full bg-[#04227D]">
          <LatestNews />
        </div>

        {/* The Latest News text will be absolutely positioned to the left */}
        {isLatestOpen && (
          <div className="animate-fab-latest-news absolute top-[1px] left-0 h-[48px] w-[165px] rounded-[50px] bg-[#047AFF] pr-[50px] pl-4 text-left text-[14px] leading-[48px] font-bold text-white">
            Latest News
          </div>
        )}
      </Link>
      <Link
        url=""
        className={clsx(
          'fixed right-7 bottom-[3%] z-50 h-[50px] w-[50px] overflow-hidden rounded-full md:hidden lg:left-[65px]',
        )}
      >
        <div className="relative flex h-[48px] w-[48px] items-center justify-center rounded-full bg-[#FF5542] text-center text-[16px] leading-[17px] text-white">
          Try
          <br />
          Now
        </div>
      </Link>
    </>
  );
}

export default Fab;
