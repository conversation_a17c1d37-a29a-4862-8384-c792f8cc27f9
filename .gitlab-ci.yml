stages:
  - build_image
  - tf_validate
  - tf_plan
  - tf_apply
  - rolling_update

default:
  tags:
    - ali-runner

  image:
    name: hashicorp/terraform:1.5.7
    entrypoint:
      - '/usr/bin/env'
      - 'PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'

build_image:
  stage: build_image
  image: docker:24.0.5
  variables:
    DOCKER_TLS_CERTDIR: ''
    DOCKER_DRIVER: overlay2
  before_script:
    - apk add --no-cache docker-cli
    - |
      echo "Waiting for Docker daemon to be ready..."
      for i in $(seq 1 30); do
        docker info && break
        echo "Waiting ($i)..."
        sleep 1
      done
  script:
    - docker info
    - echo $ALICLOUD_ACR_PASSWORD | docker login --username=h7-cicd@5261184160278876 hiseven-registry-vpc.ap-southeast-1.cr.aliyuncs.com --password-stdin
    - docker build -f ./Dockerfile
      --tag hiseven-registry-vpc.ap-southeast-1.cr.aliyuncs.com/baymax/echodata:$CI_COMMIT_SHORT_SHA
      --build-arg ENV=$NODE_ENV --build-arg VERSION=$VERSION
      --build-arg ALICLOUD_ACCESS_KEY_ID=$ALICLOUD_ACCESS_KEY --build-arg ALICLOUD_SECRET_ACCESS_KEY=$ALICLOUD_SECRET_KEY --build-arg ALICLOUD_DEFAULT_REGION=$ALICLOUD_REGION --build-arg ALICLOUD_ENDPOINT='oss-ap-southeast-1.aliyuncs.com'
      --no-cache .
    - docker push hiseven-registry-vpc.ap-southeast-1.cr.aliyuncs.com/baymax/echodata:$CI_COMMIT_SHORT_SHA
  environment:
    name: $ENV
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^(feat|bug)\//
      variables:
        ENV: staging
        NODE_ENV: stg
        VERSION: $CI_COMMIT_SHORT_SHA
      when: manual
    - if: $CI_COMMIT_BRANCH == 'master'
      variables:
        ENV: staging
        NODE_ENV: stg
        VERSION: $CI_COMMIT_SHORT_SHA
    - if: $CI_COMMIT_TAG =~ /^hi7-v(?:\d+.){2}(?:\d+)$/
      variables:
        ENV: production
        NODE_ENV: prod
        VERSION: $CI_COMMIT_TAG

tf_validate:
  stage: tf_validate
  script:
    - echo ENV $ENV
    - terraform -chdir=./.deploy/terraform init
    - terraform -chdir=./.deploy/terraform validate
  environment:
    name: $ENV
  dependencies:
    - build_image
  rules:
    - if: $CI_COMMIT_BRANCH == 'master'
      changes:
        - .deploy/terraform/**/*
        - .gitlab-ci.yml
      variables:
        ENV: staging
    - if: $CI_COMMIT_TAG =~ /^hi7-v(?:\d+.){2}(?:\d+)$/
      changes:
        - .deploy/terraform/**/*
      variables:
        ENV: production

tf_plan:
  stage: tf_plan
  script:
    - . ./.deploy/common.sh
    - echo ALICLOUD_ACCESS_KEY $ALICLOUD_ACCESS_KEY
    - echo ALICLOUD_SECRET_KEY $ALICLOUD_SECRET_KEY
    - echo ENV $ENV
    - terraform -chdir=./.deploy/terraform init
    - terraform -chdir=./.deploy/terraform workspace select -or-create $ENV
    - terraform -chdir=./.deploy/terraform workspace list
    - terraform -chdir=./.deploy/terraform plan
      -var image_tag=$CI_COMMIT_SHORT_SHA
      -var service_name=$SERVICE_NAME
      -var environment=$ENV
      -out "planfile"
  environment:
    name: $ENV
  dependencies:
    - tf_validate
  artifacts:
    paths:
      - ./.deploy/terraform/planfile
    reports:
      dotenv: build.env
  rules:
    - if: $CI_COMMIT_BRANCH == 'master'
      changes:
        - .deploy/terraform/**/*
        - .gitlab-ci.yml
      variables:
        ENV: staging
    - if: $CI_COMMIT_TAG =~ /^hi7-v(?:\d+.){2}(?:\d+)$/
      changes:
        - .deploy/terraform/**/*
        - .gitlab-ci.yml
      variables:
        ENV: production

tf_apply:
  stage: tf_apply
  script:
    - echo ALICLOUD_ACCESS_KEY $ALICLOUD_ACCESS_KEY
    - echo ALICLOUD_SECRET_KEY $ALICLOUD_SECRET_KEY
    - echo ENV $ENV
    - terraform -chdir=./.deploy/terraform init
    - terraform -chdir=./.deploy/terraform workspace select $ENV
    - terraform -chdir=./.deploy/terraform workspace list
    - terraform -chdir=./.deploy/terraform apply -input=false "planfile"
  environment:
    name: $ENV
  dependencies:
    - tf_plan
  artifacts:
    reports:
      dotenv: build.env
  rules:
    - if: $CI_COMMIT_BRANCH == 'master'
      changes:
        - .deploy/terraform/**/*
        - .gitlab-ci.yml
      variables:
        ENV: staging
    - if: $CI_COMMIT_TAG =~ /^hi7-v(?:\d+.){2}(?:\d+)$/
      changes:
        - .deploy/terraform/**/*
        - .gitlab-ci.yml
      variables:
        ENV: production

rolling_update:
  stage: rolling_update
  image: hiseven-registry.ap-southeast-1.cr.aliyuncs.com/base/gitlab/aliyuncli:3.0.209-v1
  script:
    - . ./.deploy/common.sh
    - echo "Entering rolling update stage"
    - echo ENV $ENV
    - export TAG=$CI_COMMIT_SHORT_SHA
    - echo TAG $TAG
    - aliyun configure set --profile default --mode AK --region ap-southeast-1 --access-key-id ${ALICLOUD_ACCESS_KEY} --access-key-secret ${ALICLOUD_SECRET_KEY}
    - . ./.deploy/rollingupdate.sh
  environment:
    name: $ENV
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^(feat|bug)\//
      variables:
        ENV: staging
        VERSION: $CI_COMMIT_SHORT_SHA
    - if: $CI_COMMIT_BRANCH == 'master'
      variables:
        ENV: staging
        VERSION: $CI_COMMIT_SHORT_SHA
    - if: $CI_COMMIT_TAG =~ /^hi7-v(?:\d+.){2}(?:\d+)$/
      variables:
        ENV: production
        VERSION: $CI_COMMIT_TAG
