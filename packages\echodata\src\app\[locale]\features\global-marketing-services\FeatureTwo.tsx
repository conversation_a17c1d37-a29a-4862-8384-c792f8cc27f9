'use client';

import ImageBgMobile from '@hi7/assets/background/global-marketing-bg-mobile.png';
import ImageBg from '@hi7/assets/background/global-marketing-bg.png';
import EmailMarketing from '@hi7/assets/icon/email-marketing.svg';
import SMSMarketing from '@hi7/assets/icon/sms-marketing.svg';
import VoiceMarketing from '@hi7/assets/icon/voice-marketing.svg';
import useScreenSize from '@hi7/helpers/useScreenSize';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

function FeatureTwo({ dictionary }: DictionaryProps) {
  const { isMobile } = useScreenSize();

  const cardGroupsDesktop = [
    {
      icon: VoiceMarketing,
      items: [
        {
          title:
            dictionary.features.globalMarketingServices.section2.VoiceMarketing
              .title1,
          desc: dictionary.features.globalMarketingServices.section2
            .VoiceMarketing.desc1,
        },
        {
          title:
            dictionary.features.globalMarketingServices.section2.VoiceMarketing
              .title2,
          desc: dictionary.features.globalMarketingServices.section2
            .VoiceMarketing.desc2,
        },
      ],
    },
    {
      icon: SMSMarketing,
      items: [
        {
          title:
            dictionary.features.globalMarketingServices.section2.SMSMarketing
              .title1,
          desc: dictionary.features.globalMarketingServices.section2
            .SMSMarketing.desc1,
        },
        {
          title:
            dictionary.features.globalMarketingServices.section2.SMSMarketing
              .title2,
          desc: dictionary.features.globalMarketingServices.section2
            .SMSMarketing.desc2,
        },
      ],
    },
    {
      icon: EmailMarketing,
      items: [
        {
          title:
            dictionary.features.globalMarketingServices.section2.EmailMarketing
              .title1,
          desc: dictionary.features.globalMarketingServices.section2
            .EmailMarketing.desc1,
        },
        {
          title:
            dictionary.features.globalMarketingServices.section2.EmailMarketing
              .title2,
          desc: dictionary.features.globalMarketingServices.section2
            .EmailMarketing.desc2,
        },
        {
          title:
            dictionary.features.globalMarketingServices.section2.EmailMarketing
              .title3,
          desc: dictionary.features.globalMarketingServices.section2
            .EmailMarketing.desc3,
        },
        {
          title:
            dictionary.features.globalMarketingServices.section2.EmailMarketing
              .title4,
          desc: dictionary.features.globalMarketingServices.section2
            .EmailMarketing.desc4,
        },
      ],
    },
  ];

  // Mobile version with swapped VoiceMarketing and SMSMarketing
  const cardGroupsMobile = [
    {
      icon: SMSMarketing,
      items: [
        {
          title:
            dictionary.features.globalMarketingServices.section2.SMSMarketing
              .title1,
          desc: dictionary.features.globalMarketingServices.section2
            .SMSMarketing.desc1,
        },
        {
          title:
            dictionary.features.globalMarketingServices.section2.SMSMarketing
              .title2,
          desc: dictionary.features.globalMarketingServices.section2
            .SMSMarketing.desc2,
        },
      ],
    },
    {
      icon: VoiceMarketing,
      items: [
        {
          title:
            dictionary.features.globalMarketingServices.section2.VoiceMarketing
              .title1,
          desc: dictionary.features.globalMarketingServices.section2
            .VoiceMarketing.desc1,
        },
        {
          title:
            dictionary.features.globalMarketingServices.section2.VoiceMarketing
              .title2,
          desc: dictionary.features.globalMarketingServices.section2
            .VoiceMarketing.desc2,
        },
      ],
    },
    {
      icon: EmailMarketing,
      items: [
        {
          title:
            dictionary.features.globalMarketingServices.section2.EmailMarketing
              .title1,
          desc: dictionary.features.globalMarketingServices.section2
            .EmailMarketing.desc1,
        },
        {
          title:
            dictionary.features.globalMarketingServices.section2.EmailMarketing
              .title2,
          desc: dictionary.features.globalMarketingServices.section2
            .EmailMarketing.desc2,
        },
        {
          title:
            dictionary.features.globalMarketingServices.section2.EmailMarketing
              .title3,
          desc: dictionary.features.globalMarketingServices.section2
            .EmailMarketing.desc3,
        },
        {
          title:
            dictionary.features.globalMarketingServices.section2.EmailMarketing
              .title4,
          desc: dictionary.features.globalMarketingServices.section2
            .EmailMarketing.desc4,
        },
      ],
    },
  ];

  const cardGroups = isMobile ? cardGroupsMobile : cardGroupsDesktop;

  return (
    <div className="mt-10 flex min-h-screen items-center justify-center lg:h-screen">
      <div className="w-full">
        <div className="h-full">
          <div className="relative min-h-screen">
            <div className="relative h-full w-full">
              <Image
                src={ImageBgMobile}
                alt={'background'}
                className="w-[100vw] object-cover lg:hidden"
                priority
              />

              <Image
                src={ImageBg}
                alt={'background'}
                className="hidden w-[100vw] object-cover lg:mt-0 lg:mb-[50px] lg:block lg:scale-y-[1.05] lg:scale-y-[1] xl:mt-15"
              />
            </div>

            {/******* Content Overlay *******/}
            <div
              className={`w-full text-start text-white transition-opacity duration-700 md:static md:top-3/5 lg:absolute lg:top-50 lg:h-[100%]`}
            >
              <h2
                className={`absolute top-50 right-5 w-[70%] px-4 text-right text-[40px] leading-[42px] font-bold ${arsenal.className} md:top-100 md:w-[80%] md:text-[50px] md:leading-[30px] lg:static lg:-top-[30px] lg:w-full lg:px-15 xl:px-20 xl:text-[70px]`}
              >
                {dictionary.features.globalMarketingServices.title}
              </h2>
              <div className="relative z-[2] -mt-20 -mb-3 min-h-screen bg-[#047AFF] px-8 pt-10 md:-mt-40 md:pb-20 lg:mt-0 lg:bg-transparent lg:px-15 xl:px-20 xl:pt-15">
                <div className="block items-stretch justify-center gap-6 lg:flex lg:flex-row-reverse lg:gap-10">
                  {cardGroups.map((group, index) => (
                    <div
                      key={index}
                      className={`flex h-full w-full flex-col rounded-[20px] bg-white p-6 text-[#FF5C00] xl:px-10 ${index === cardGroups.length - 1 ? 'self-end lg:grid lg:w-[108%] xl:h-[60vh]' : 'mb-8 lg:mb-0 lg:h-[67vh] lg:w-[60%] xl:h-[70vh]'} `}
                    >
                      <group.icon className="mb-3" />
                      <div
                        key={index}
                        className={`flex flex-1 flex-col justify-between ${index === cardGroups.length - 1 ? 'w-full gap-5 lg:grid lg:grid-cols-2 xl:gap-7' : 'h-full lg:w-full'}`}
                      >
                        {group.items.map((item, idx) => (
                          <div key={idx} className="flex h-full flex-col">
                            <h3 className="text-[23px] leading-[26px] font-medium text-[#FF5C00] lg:text-[15px] lg:leading-[18px] xl:text-[30px] xl:leading-[33px]">
                              {item.title}
                            </h3>
                            <hr className="my-3 lg:my-2" />
                            <p className="mb-10 text-[18px] leading-[22px] font-normal text-[#04227D] lg:mb-4 lg:text-[14px] lg:leading-[18px] xl:text-[22px] xl:leading-[28px]">
                              {item.desc}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default FeatureTwo;
