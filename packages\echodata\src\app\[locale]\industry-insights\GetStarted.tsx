import type { Dictionary } from '@hi7/interface/dictionary';
import { Arsenal } from 'next/font/google';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
});

interface GetStartedProps {
  dictionary: Dictionary;
}

function GetStarted({ dictionary }: GetStartedProps) {
  const baseStyle =
    'inline-block text-center w-[155px] cursor-pointer rounded-4xl py-2 text-sm font-semibold text-white transition duration-200 2xl:w-[200px] 2xl:py-3 2xl:text-lg ;';

  const redButtonStyle = `${baseStyle} bg-[#FF5542] hover:bg-white hover:text-[#FF5542]`;
  const blueButtonStyle = `${baseStyle} bg-[#047AFF] hover:bg-white hover:text-[#FF5542]`;

  return (
    <div className="relative z-10 mt-[-5vh] bg-gradient-to-b from-transparent to-[#04227D] to-80% md:to-50%">
      <div className="pointer-events-none relative h-[20dvh] w-full lg:h-[45vh]" />

      <div className="relative px-8 pb-13 text-center text-white 2xl:pb-18">
        <h2
          className={`${arsenal.className} text-[11vw] leading-none font-bold md:text-[4.3vw]`}
        >
          {dictionary.general.freeTrial.title2}
        </h2>

        <div className="mt-8 flex flex-col items-center justify-center gap-3 md:mt-4 md:flex-row">
          <a
            href="https://t.me/Abb007tgbot"
            target="_blank"
            rel="noopener noreferrer"
            className={redButtonStyle}
          >
            {dictionary.general.freeTrial.button1}
          </a>
          <a
            href="https://mall.007tg.com/"
            target="_blank"
            rel="noopener noreferrer"
            className={blueButtonStyle}
          >
            {dictionary.general.freeTrial.button6}
          </a>
        </div>
      </div>
    </div>
  );
}

export default GetStarted;
