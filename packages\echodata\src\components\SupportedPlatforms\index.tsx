import app1 from '@hi7/assets/logo/app1-talk.png';
import app10 from '@hi7/assets/logo/app10-bybit.png';
import app11 from '@hi7/assets/logo/app11-skype.png';
import app12 from '@hi7/assets/logo/app12-swiggy.png';
import app13 from '@hi7/assets/logo/app13-moj.png';
import app14 from '@hi7/assets/logo/app14-vn-pay.png';
import app15 from '@hi7/assets/logo/app15-cash.png';
import app16 from '@hi7/assets/logo/app16-icic-bank.png';
import app17 from '@hi7/assets/logo/app17-flipkart.png';
import app18 from '@hi7/assets/logo/app18-mercado.png';
import app19 from '@hi7/assets/logo/app19-mail-detect.png';
import app2 from '@hi7/assets/logo/app2-okx.png';
import app3 from '@hi7/assets/logo/app3-cian.png';
import app4 from '@hi7/assets/logo/app4-snapchat.png';
import app5 from '@hi7/assets/logo/app5-ios.png';
import app6 from '@hi7/assets/logo/app6-ios-blue.png';
import app7 from '@hi7/assets/logo/app7-operators.png';
import app8 from '@hi7/assets/logo/app8-phone-call.png';
import app9 from '@hi7/assets/logo/app9-momo.png';

import pf1 from '@hi7/assets/logo/pf1-coinbase.png';
import pf10 from '@hi7/assets/logo/pf10-grab.png';
import pf11 from '@hi7/assets/logo/pf11-insta.png';
import pf12 from '@hi7/assets/logo/pf12-zalo.png';
import pf13 from '@hi7/assets/logo/pf13-facebook.png';
import pf14 from '@hi7/assets/logo/pf14-signal.png';
import pf15 from '@hi7/assets/logo/pf15-hh.png';
import pf16 from '@hi7/assets/logo/pf16-band.png';
import pf17 from '@hi7/assets/logo/pf17-rcs.png';
import pf18 from '@hi7/assets/logo/pf18-microsoft.png';
import pf19 from '@hi7/assets/logo/pf19-mastercard.png';
import pf2 from '@hi7/assets/logo/pf2-linkedIn.png';
import pf20 from '@hi7/assets/logo/pf20-botim.png';
import pf21 from '@hi7/assets/logo/pf21-mint.png';
import pf22 from '@hi7/assets/logo/pf22-vk.png';
import pf23 from '@hi7/assets/logo/pf23-htx.png';
import pf24 from '@hi7/assets/logo/pf24-nasdaq.png';
import pf25 from '@hi7/assets/logo/pf25-viber.png';
import pf3 from '@hi7/assets/logo/pf3-amazon.png';
import pf4 from '@hi7/assets/logo/pf4-tikTok.png';
import pf5 from '@hi7/assets/logo/pf5-whatsapp.png';
import pf6 from '@hi7/assets/logo/pf6-finance.png';
import pf7 from '@hi7/assets/logo/pf7-telegram.png';
import pf8 from '@hi7/assets/logo/pf8-x.png';
import pf9 from '@hi7/assets/logo/pf9-line.png';
// import type { Dictionary } from '@hi7/interface/dictionary';
import type { StaticImageData } from 'next/image';
import NextImage from 'next/image';

const PLATFORMS1 = [
  pf1,
  pf2,
  pf3,
  pf4,
  pf5,
  pf6,
  pf7,
  pf8,
  pf9,
  pf10,
  pf11,
  pf12,
  pf13,
  pf14,
  pf15,
  pf16,
  pf17,
  pf18,
  pf19,
  pf20,
  pf21,
  pf22,
  pf23,
  pf24,
  pf25,
];

const PLATFORMS2 = [
  app1,
  app2,
  app3,
  app4,
  app5,
  app6,
  app7,
  app8,
  app9,
  app10,
  app11,
  app12,
  app13,
  app14,
  app15,
  app16,
  app17,
  app18,
  app19,
];

const InfiniteSliderItem = ({
  logo,
  index,
}: {
  logo: StaticImageData;
  index: number;
}) => {
  return (
    <div
      className="slide my-1 flex h-16 min-w-fit items-center justify-center px-6 lg:my-2 lg:h-19 xl:my-5 xl:h-22"
      key={index}
    >
      <NextImage
        src={logo}
        alt="logo"
        className="h-[40px] w-auto object-contain lg:h-[40px] xl:h-[52px]"
        width={120}
        height={60}
      />
    </div>
  );
};

const InfiniteSlider = ({
  location,
  // dictionary,
}: {
  location: '1st' | '2nd';
  // dictionary: Dictionary;
}) => {
  const PLATFORMS = location === '1st' ? PLATFORMS1 : PLATFORMS2;

  return (
    <div className="m-auto overflow-hidden">
      <div className="animate-infinite-slider flex whitespace-nowrap">
        {PLATFORMS.map((logo, index) => (
          <InfiniteSliderItem logo={logo} index={index} key={`set1-${index}`} />
        ))}
        {PLATFORMS.map((logo, index) => (
          <InfiniteSliderItem logo={logo} index={index} key={`set2-${index}`} />
        ))}
      </div>
    </div>
  );
};

export default InfiniteSlider;
