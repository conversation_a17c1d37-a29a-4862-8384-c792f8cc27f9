import MeetingImg from '@hi7/assets/background/meeting-background.jpg';
import Union from '@hi7/assets/background/union-global-data.svg?url';
import Point2 from '@hi7/assets/icon/check-screen.svg';
import Point1 from '@hi7/assets/icon/feature6.svg';
import Point3 from '@hi7/assets/icon/global-data.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import clsx from 'clsx';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

function FeatureOne({ dictionary }: DictionaryProps) {
  const points = [
    {
      icon: Point1,
      title:
        dictionary.features.globalDataAcquisition.feature1.globalDataSupport
          .title,
      desc: dictionary.features.globalDataAcquisition.feature1.globalDataSupport
        .desc,
    },
    {
      icon: Point2,
      title:
        dictionary.features.globalDataAcquisition.feature1
          .cloudBasedDataDeduplication.title,
      desc: dictionary.features.globalDataAcquisition.feature1
        .cloudBasedDataDeduplication.desc,
    },
    {
      icon: Point3,
      title:
        dictionary.features.globalDataAcquisition.feature1.diverseProductFormats
          .title,
      desc: dictionary.features.globalDataAcquisition.feature1
        .diverseProductFormats.desc,
    },
  ];

  return (
    <section className="relative mt-30 h-auto overflow-x-clip md:mt-30 lg:mt-40 lg:h-screen lg:translate-y-[10%] xl:translate-y-[10%]">
      <div className="flex h-auto flex-row md:flex-row lg:hidden lg:h-[130px]">
        <div
          className={clsx(
            'z-10 order-2 -mb-5 flex w-full items-end justify-end rounded-t-[40px] bg-[#E9F3FF] pr-6',
            'md:order-1 md:mb-0 md:w-1/3 md:pr-10',
            'lg:hidden',
          )}
        >
          <h2
            className={`${arsenal.className} text-right text-[40px] leading-[36px] font-bold whitespace-pre-line text-[#FF5542] md:text-[45px] lg:text-left lg:text-[64px] lg:leading-[72px]`}
            dangerouslySetInnerHTML={{
              __html: dictionary.features.globalDataAcquisition.feature1.title,
            }}
          />
        </div>

        <div className="relative order-1 w-full md:order-2 md:w-2/3 lg:w-5/9">
          <Image
            src={MeetingImg}
            alt={'insight-img'}
            className={clsx(
              '-mt-28 h-[30vh] w-full rounded-r-[45px] border-t-25 border-r-25 border-b-25 border-white object-cover',
              'md:mr-[140px] md:rounded-[70px] md:rounded-r-none md:border-r-0 md:border-l-25',
              'lg:mx-50 lg:mt-0 lg:-ml-0 lg:h-[50vh] lg:translate-y-[-5%] lg:rounded-[85px] lg:border-r-[45px] lg:border-b-30 lg:border-l-35',
              'xl:h-[45vh] xl:translate-y-[-20%]',
            )}
          />
        </div>
      </div>

      <div className="hidden lg:flex lg:flex-row lg:items-end">
        <Image
          src={Union}
          alt="union"
          className="h-[29.9dvh] w-full"
          priority
        />
        <h2
          className={`${arsenal.className} absolute left-15 text-right text-[40px] leading-[36px] font-bold text-[#FF5542] md:text-[45px] lg:ml-7 lg:text-left lg:text-[64px] lg:leading-[72px] xl:ml-16`}
          dangerouslySetInnerHTML={{
            __html: dictionary.features.globalDataAcquisition.feature1.title,
          }}
        />
        <Image
          src={MeetingImg}
          alt={'insight-img'}
          className={clsx(
            'absolute right-6 mb-4 h-[30vh] w-[50%] object-cover',
            'lg:-ml-0 lg:h-[35vh] lg:translate-y-[-5%] lg:rounded-[70px]',
            'xl:translate-y-[-5%]',
          )}
        />
      </div>

      <div className="-mt-[50px] rounded-br-[40px] bg-[#E9F3FF] pt-20 lg:-mt-1 lg:rounded-tr-[80px] lg:rounded-b-[80px] lg:px-7 lg:pt-15">
        <hr className="mx-auto w-[90%] border-[#FF5542] xl:mt-8 xl:w-[89%]" />
        <div className="mt-6 grid grid-cols-1 gap-0 md:grid-cols-2 lg:mt-0 lg:grid-cols-3 lg:px-6 lg:pb-6 xl:mt-5 xl:gap-13">
          {points.length > 0 &&
            points.map((point, index) => (
              <div
                key={index}
                className="mb-0 flex w-full flex-col gap-4 rounded-[30px] bg-[#E9F3FF] p-7 lg:px-10 lg:py-8 xl:px-20"
              >
                <div className="flex flex-row items-center justify-start text-[#FF5542] xl:justify-start">
                  <point.icon className="w-[30%] xl:w-[20%]" />
                  <h1 className="w-[80%] text-[24px] leading-[25px] font-[600] lg:text-[24px] xl:text-[29px] xl:leading-[30px]">
                    {point.title}
                  </h1>
                </div>
                <hr />
                <span className="text-[18px] font-[400] text-[#04227D] xl:text-[25px]">
                  {point.desc}
                </span>
              </div>
            ))}
        </div>
      </div>
    </section>
  );
}

export default FeatureOne;
