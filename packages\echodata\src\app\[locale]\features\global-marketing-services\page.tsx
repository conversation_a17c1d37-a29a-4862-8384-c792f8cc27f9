import GetStarted from '@hi7/app/[locale]/features/global-marketing-services/GetStarted';
import Landing from '@hi7/app/[locale]/features/global-marketing-services/Landing';
import PageWrapper from '@hi7/components/PageWrapper';
import type { Dictionary } from '@hi7/interface/dictionary';
import { getDictionary, type Locale } from '@hi7/lib/i18n';
import FeatureOne from './FeatureOne';
import FeatureTwo from './FeatureTwo';

type PageProps = {
  params: {
    locale: Locale;
  };
};

export default async function Page({ params }: PageProps) {
  const { locale } = params;
  const t: Dictionary = await getDictionary(locale);

  const sectionConfigs = [
    { scrollDirection: 'default' as const, scrollFull: true },
    { scrollDirection: 'default' as const, scrollFull: true },
    { scrollDirection: 'default' as const, scrollFull: true },
    { scrollDirection: 'default' as const, scrollFull: true, scrollSkip: true },
    { scrollDirection: 'default' as const },
  ];

  const ScrollBgGradient = () => (
    <div className="h-[33vh] w-full bg-gradient-to-b from-[#047aff] to-[#04227D] to-100% lg:block lg:h-[50vh]"></div>
  );

  return (
    <PageWrapper sectionConfigs={sectionConfigs}>
      <Landing dictionary={t} />
      <FeatureOne dictionary={t} />
      <FeatureTwo dictionary={t} />
      <ScrollBgGradient />
      <GetStarted dictionary={t} />
    </PageWrapper>
  );
}
