'use client';

import HomepageBgMobile1 from '@hi7/assets/background/homepage-bg1-mobile.png';
import HomepageBg1 from '@hi7/assets/background/homepage-bg1.png';
import HomepageBgMobile2 from '@hi7/assets/background/homepage-bg2-mobile.png';
import HomepageBg2 from '@hi7/assets/background/homepage-bg2.png';
import HomepageBgMobile3 from '@hi7/assets/background/homepage-bg3-mobile.png';
import HomepageBg3 from '@hi7/assets/background/homepage-bg3.png';
import HomepageLongdVector from '@hi7/assets/icon/homepage-long-vector.png';
import HomepageRoundVector from '@hi7/assets/icon/homepage-round-vector.png';

import RoundChevronLeft from '@hi7/assets/icon/round-chevron-left.svg';
import RoundChevronRight from '@hi7/assets/icon/round-chevron-right.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { useSwipeable } from 'react-swipeable';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

function Landing({ dictionary }: DictionaryProps) {
  const slides = [
    {
      title: dictionary.home.slide.title1,
      subtitle: dictionary.home.slide.subtitle1,
      significantText1: ' 200+',
      significantText2: ' 60+',
      desktopImage: HomepageBg1,
      mobileImage: HomepageBgMobile1,
    },
    {
      title: dictionary.home.slide.title2,
      subtitle: dictionary.home.slide.subtitle2,
      significantText1: ' 200+',
      significantText2: ' 60+',
      desktopImage: HomepageBg2,
      mobileImage: HomepageBgMobile2,
    },
    {
      title: dictionary.home.slide.title3,
      subtitle: dictionary.home.slide.subtitle2,
      significantText1: ' 200+',
      significantText2: ' 60+',
      desktopImage: HomepageBg3,
      mobileImage: HomepageBgMobile3,
    },
  ];

  const [currentSlide, setCurrentSlide] = useState(0);
  const [animate, setAnimate] = useState(false);
  const [isPrevAnimating, setIsPrevAnimating] = useState(false);
  const [isNextAnimating, setIsNextAnimating] = useState(false);

  const triggerAnimation = () => {
    setAnimate(false);
    setTimeout(() => setAnimate(true), 10); // Re-trigger animation
  };

  const nextSlide = () => {
    triggerAnimation();
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    triggerAnimation();
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const handleNextClick = () => {
    nextSlide();
    setIsNextAnimating(true);
    setTimeout(() => setIsNextAnimating(false), 300);
  };

  const handlePrevClick = () => {
    prevSlide();
    setIsPrevAnimating(true);
    setTimeout(() => setIsPrevAnimating(false), 300);
  };

  const handlers = useSwipeable({
    onSwipedLeft: nextSlide,
    onSwipedRight: prevSlide,
    preventScrollOnSwipe: true,
    trackMouse: true,
  });

  const current = slides[currentSlide];

  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide();
    }, 3000);

    return () => clearInterval(interval);
  }, [currentSlide]);

  return (
    <div
      className="relative flex items-center justify-center overflow-hidden"
      {...handlers}
    >
      <div className="w-full">
        <div className="h-screen">
          {/******* Mobile *******/}
          <div className="relative block h-[95%] lg:hidden">
            <div className="relative h-full w-full transition-all duration-700 ease-in-out">
              <Image
                fill
                src={current.mobileImage}
                alt={current.title}
                className="object-cover"
                priority
              />
            </div>

            {/******* Text Overlay *******/}
            <div
              className={`absolute top-25 z-10 w-full px-4 text-start text-white transition-opacity duration-700 ${animate && 'animate-slide-in-left'}`}
            >
              <h1
                className={`mb-3 text-[40px] leading-[42px] font-bold drop-shadow-md ${arsenal.className}`}
              >
                {current.title}
              </h1>
              <p className="text-md mb-3 font-thin">{current.subtitle}</p>
              <hr className="mb-3" />
              <h3 className="mb-3">
                <span className="align-top text-[14px] font-light">
                  {dictionary.home.landing.supportedIn}
                </span>
                <span className="align-top text-[33px] font-bold">
                  {current.significantText1}
                </span>
                <span className="align-bottom text-[23px] font-thin">
                  {' '}
                  {dictionary.home.landing.countries}
                </span>
              </h3>
              <hr className="mb-3" />
              <h3 className="mb-3">
                <span className="align-top text-[14px] font-light">
                  {dictionary.home.landing.integratedWith}{' '}
                </span>
                <span className="align-top text-[33px] font-bold">
                  {current.significantText2}
                </span>
                <span className="align-bottom text-[23px] font-thin">
                  {' '}
                  {dictionary.home.landing.appPlatforms}
                </span>
              </h3>
              <hr className="mb-6" />
              <a
                href="#"
                target="_blank"
                className="block w-auto max-w-[200px] cursor-pointer rounded-[25px] bg-[#FF5542] px-[30px] py-0 text-center text-[18px] leading-[40px] font-bold whitespace-nowrap text-white hover:bg-white hover:text-[#FF5542]"
              >
                {dictionary.general.freeTrial.button1}
              </a>
            </div>

            {/******* Slide Indicators *******/}
            <div className="absolute right-8 bottom-14 z-20 flex w-full justify-end gap-3">
              {slides.map((slide, idx) => (
                <span
                  key={idx}
                  style={{
                    backgroundColor:
                      idx === currentSlide ? '#fc5444' : '#047aff',
                  }}
                  className="size-4 rounded-full"
                />
              ))}
            </div>
          </div>

          {/****** Desktop ******/}
          <div className="relative hidden items-center justify-center lg:flex lg:h-[85%]">
            {slides.map((slide, idx) => (
              <Image
                key={idx}
                width={1920}
                src={slide.desktopImage}
                alt={slide.title}
                className={`absolute object-cover transition-opacity duration-700 ${idx === currentSlide ? 'opacity-100' : 'opacity-0'}`}
              />
            ))}
            <div
              className={`absolute top-30 left-17 z-10 text-start whitespace-break-spaces text-white transition-opacity duration-700 xl:top-35 xl:left-24 ${animate && 'animate-slide-in-left'}`}
            >
              <h1
                className={`mb-5 text-[48px] leading-[55px] font-bold tracking-[1px] xl:mb-9 xl:text-[64px] xl:leading-[68px] ${arsenal.className}`}
              >
                {current.title}
              </h1>
              <p className="mb-3 text-[24px] leading-[30px] font-thin xl:mb-10">
                {current.subtitle}
              </p>
              <hr className="mb-5 w-[620px] xl:mb-3 xl:w-[950px]" />
              <h3 className="mb-0 xl:mb-7">
                <span className="text-[20px] font-[500] xl:text-[24px]">
                  {dictionary.home.landing.supportedIn}
                </span>
                <span className="text-[40px] font-bold xl:text-[48px]">
                  {current.significantText1}
                </span>
                <span className="text-[40px] font-thin xl:text-[48px]">
                  {' '}
                  {dictionary.home.landing.countries}
                </span>
              </h3>
              <hr className="mb-3 w-[620px] xl:w-[950px]" />
              <h3 className="mb-0 xl:mb-7">
                <span className="text-[20px] font-[500] xl:text-[24px]">
                  {dictionary.home.landing.integratedWith}{' '}
                </span>
                <span className="text-[40px] font-bold xl:text-[48px]">
                  {current.significantText2}
                </span>
                <span className="text-[40px] font-thin xl:text-[48px]">
                  {' '}
                  {dictionary.home.landing.appPlatforms}
                </span>
              </h3>
              <hr className="mb-6 w-[620px] xl:mb-10 xl:w-[950px]" />
              <a
                href="#"
                target="_blank"
                className="block w-auto max-w-[200px] cursor-pointer rounded-[25px] bg-[#FF5542] px-[30px] py-0 text-center text-[18px] leading-[40px] font-bold whitespace-nowrap text-white hover:bg-white hover:text-[#FF5542]"
              >
                {dictionary.general.freeTrial.button1}
              </a>
            </div>

            {/* Navigation Arrows */}
            <div className="absolute right-2/11 bottom-1/7 z-20 flex md:right-1/5 md:bottom-1/6 lg:right-1/7 lg:bottom-1/9 xl:right-1/6 xl:bottom-1/6">
              <RoundChevronLeft
                className={`transform cursor-pointer transition-all duration-300 ease-in-out select-none hover:scale-125 ${
                  isPrevAnimating
                    ? 'scale-50 opacity-0'
                    : 'scale-100 opacity-100'
                }`}
                onClick={handlePrevClick}
              />
              <RoundChevronRight
                className={`transform cursor-pointer transition-all duration-300 ease-in-out select-none hover:scale-125 ${
                  isNextAnimating
                    ? 'scale-50 opacity-0'
                    : 'scale-100 opacity-100'
                }`}
                onClick={handleNextClick}
              />
            </div>
          </div>
          <div className="z-20 hidden w-full lg:flex">
            <Image
              src={HomepageRoundVector}
              alt="icon"
              className="animate-slide-in-right absolute right-1/9 bottom-0 h-[22%] w-[22%] object-contain select-none"
            />
            <Image
              src={HomepageLongdVector}
              alt="icon"
              className="animate-slide-in-right absolute -right-15 -bottom-10 scale-90 select-none xl:right-0 xl:bottom-0 xl:scale-130"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default Landing;
