import Feature1 from '@hi7/assets/icon/feature1.svg';
import Feature2 from '@hi7/assets/icon/feature2.svg';
import Feature3 from '@hi7/assets/icon/feature3.svg';
import Feature4 from '@hi7/assets/icon/feature4.svg';
import Feature5 from '@hi7/assets/icon/feature5.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import TriggerAnimation from '../TriggerAnimation';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'auto',
});

function Feature({ dictionary }: DictionaryProps) {
  const features = [
    {
      icon: Feature1,
      title: dictionary.home.feature1.globalNumberGeneration.title,
      desc: dictionary.home.feature1.globalNumberGeneration.desc,
      points: dictionary.home.feature1.globalNumberGeneration.points,
    },
    {
      icon: Feature2,
      title: dictionary.home.feature1.whatsappFiltering.title,
      desc: dictionary.home.feature1.whatsappFiltering.desc,
      points: dictionary.home.feature1.whatsappFiltering.points,
    },
    {
      icon: Feature3,
      title: dictionary.home.feature1.telegramFiltering.title,
      desc: dictionary.home.feature1.telegramFiltering.desc,
      points: dictionary.home.feature1.telegramFiltering.points,
    },
    {
      icon: Feature4,
      title: dictionary.home.feature1.oneStopMarketing.title,
      desc: dictionary.home.feature1.oneStopMarketing.desc,
      points: dictionary.home.feature1.oneStopMarketing.points,
    },
    {
      icon: Feature5,
      title: dictionary.home.feature1.taskFlowFiltering.title,
      desc: dictionary.home.feature1.taskFlowFiltering.desc,
      points: dictionary.home.feature1.taskFlowFiltering.points,
    },
  ];

  return (
    <TriggerAnimation>
      <div className="hidden h-[12vh] bg-[#ffffff] opacity-0 lg:block xl:h-[16vh]"></div>
      <div className="mt-15 flex items-center justify-center overflow-hidden rounded-[30px] rounded-bl-none bg-[#047AFF] px-8 lg:rounded-[150px] lg:rounded-br-[300px] lg:rounded-bl-none lg:pb-8 xl:px-15">
        <div className="h-full w-full">
          <div className="relative lg:min-h-[820px]">
            <div className="flex flex-col items-start justify-center pt-12 pb-15 text-white lg:items-start lg:px-10 lg:pt-30 lg:pb-0 xl:px-10 xl:pt-50 xl:pb-12">
              <h2
                className={`mb-8 text-[40px] leading-[40px] font-bold lg:-mb-10 lg:translate-y-[-100%] lg:pl-8 lg:text-[52px] lg:leading-[58px] xl:text-[78px] ${arsenal.className} xl:-mb-2 xl:pl-8`}
              >
                {dictionary.home.feature1.title}
              </h2>
              <p className="text-[16px] font-thin lg:pl-8 lg:text-[20px] xl:text-[34px]">
                {dictionary.home.feature1.desc}
              </p>
              <hr className="mt-5 w-full md:hidden" />
              <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:mt-5 lg:grid-cols-3 xl:mt-12 xl:gap-13">
                {features.length > 0 &&
                  features.map((feature, index) => (
                    <div
                      key={index}
                      className="mb-0 flex w-full flex-col gap-4 rounded-[30px] bg-[#E9F3FF] p-7 text-[#047AFF]"
                    >
                      <div className="flex flex-row items-center justify-center lg:justify-start">
                        <feature.icon className="w-[30%] lg:w-[25%] xl:w-[15%]" />
                        <h1 className="mt-1 text-[20px] leading-[25px] font-[500] lg:text-[20px] xl:text-[30px] xl:leading-[30px]">
                          {feature.title}
                        </h1>
                      </div>
                      <hr />
                      <span className="text-[16px] font-[300] lg:text-[14px] xl:text-[20px]">
                        {feature.desc}
                      </span>
                      <ul className="-mt-4 text-[16px] font-[300] lg:text-[14px] xl:text-[20px]">
                        {feature.points.split('\n').map((point, index) => (
                          <li key={index}>{point}</li>
                        ))}
                      </ul>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </TriggerAnimation>
  );
}

export default Feature;
