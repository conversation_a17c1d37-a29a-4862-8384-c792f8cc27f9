'use client';

import { defaultAnimateConfig, type VariantType } from '@hi7/configs/animation';
import useScreenSize from '@hi7/helpers/useScreenSize';
import clsx from 'clsx';
import { motion, useAnimation, useInView } from 'motion/react';
import type React from 'react';
import { useEffect, useRef } from 'react';

type MarginValue = `${number}px` | `${number}%`;

export type MarginType =
  | MarginValue
  | `${MarginValue} ${MarginValue}`
  | `${MarginValue} ${MarginValue} ${MarginValue}`
  | `${MarginValue} ${MarginValue} ${MarginValue} ${MarginValue}`;

interface AnimationFrameProps {
  variant: VariantType;
  outVariant?: VariantType;
  once?: boolean;
  className?: string;
  children: React.ReactNode;
  margin?: MarginType;
}

const AnimationFrameInOut: React.FC<AnimationFrameProps> = ({
  variant,
  outVariant,
  once = true,
  className,
  children,
  margin,
}) => {
  const ref = useRef(null);
  const controls = useAnimation();
  const isInView = useInView(ref, { margin, once });
  const { isMobile } = useScreenSize();

  const inConfig = defaultAnimateConfig[variant];
  const outConfig = outVariant ? defaultAnimateConfig[outVariant] : null;

  useEffect(() => {
    if (isInView) {
      controls.set(inConfig.initial);
      controls.start(inConfig.animate);
    } else if (outConfig) {
      controls.start(outConfig.animate);
    }
  }, [isInView, controls, inConfig, outConfig]);
  return isMobile ? (
    <div className={clsx('lg:hidden', className)}>{children}</div>
  ) : (
    <motion.div
      ref={ref}
      initial={inConfig.initial}
      animate={controls}
      transition={inConfig.transition}
      className={clsx('hidden lg:flex', className)}
    >
      {children}
    </motion.div>
  );
};

export default AnimationFrameInOut;
