'use client';

import BannerLongRoundVector from '@hi7/assets/background/elongated-round-vector-lightblue.svg';
import BannerBgImg from '@hi7/assets/background/global-marketing-bg-img.png';
import BannerBgImgMobile from '@hi7/assets/background/global-marketing-bg-mobile-img.png';
import BannerRoundVector from '@hi7/assets/background/rounded-vector-lightblue.svg';
import AnimationFrameInOut from '@hi7/components/AnimationFrameInOut';
import useScreenSize from '@hi7/helpers/useScreenSize';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

function Landing({ dictionary }: DictionaryProps) {
  const { isMobile } = useScreenSize();

  return (
    <div className="relative flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="h-screen md:mb-15 md:h-[45vh] lg:h-[60%]">
          <div className="w-full items-center justify-center lg:flex">
            <Image
              src={BannerBgImg}
              alt={'background'}
              className="hidden md:block md:h-[50vh] md:w-screen lg:h-[90vh] xl:h-[95vh]"
            />
            <Image
              src={BannerBgImgMobile}
              alt={'background'}
              className="h-full w-full object-cover md:hidden"
            />
            <div className="absolute top-23 left-7 z-10 w-[90%] pr-7 text-start whitespace-break-spaces text-[#04227D] transition-opacity duration-700 md:top-10 md:right-7 md:pr-0 md:text-end lg:top-30 xl:top-40 xl:left-24">
              <h1
                className={`mb-4 text-[40px] leading-[40px] font-bold md:w-[80%] md:justify-self-end lg:text-[64px] lg:leading-[66px] xl:mb-9 xl:text-[85px] xl:leading-[68px] ${arsenal.className}`}
              >
                {isMobile
                  ? '全球营销业务'
                  : dictionary.features.globalMarketingServices.banner.title}
              </h1>
              <div className="items-center justify-center self-center text-left md:w-[80%] md:items-end md:justify-self-end md:text-right">
                <p className="text-[16px] leading-[20px] font-[300] lg:text-[24px] lg:leading-[33px] xl:text-[40px] xl:leading-[55px]">
                  {dictionary.features.globalMarketingServices.banner.desc}
                </p>
              </div>
              <hr className="my-4 w-[100%] md:mx-auto lg:mx-0 lg:my-5 lg:w-2/3 lg:justify-self-end xl:my-8" />
              <div className="mt-8 block md:items-end md:justify-end md:justify-self-end lg:flex">
                <a
                  href="https://t.me/Abb007tgbot#"
                  target="_blank"
                  className="mt-5 block w-auto max-w-[160px] cursor-pointer rounded-[25px] bg-[#FF5542] px-[20px] py-1 text-center text-[15px] leading-[40px] font-bold whitespace-nowrap text-white hover:bg-[#C7E5FF] lg:max-w-[200px] lg:text-[18px]"
                >
                  {dictionary.general.freeTrial.button1}
                </a>
                <a
                  href="https://mall.007tg.com/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mt-5 block w-auto max-w-[160px] cursor-pointer rounded-[25px] bg-[#047AFF] px-[20px] py-1 text-center text-[15px] leading-[40px] font-bold whitespace-nowrap text-white hover:bg-[#C7E5FF] lg:ml-5 lg:max-w-[200px] lg:text-[18px]"
                >
                  {dictionary.general.freeTrial.button5}
                </a>
              </div>
            </div>
            <BannerLongRoundVector className="absolute -right-43 bottom-30 scale-55 md:hidden" />
            <BannerRoundVector className="absolute right-16 bottom-50 scale-55 md:hidden" />
          </div>
          <AnimationFrameInOut
            variant="SlideUp45AtEase"
            outVariant="SlideUp45RightAtEase"
            once={false}
            className="hidden lg:block"
          >
            <BannerLongRoundVector className="md:absolute lg:top-40 lg:left-90 lg:rotate-90 xl:top-40 xl:left-150 xl:scale-130" />
            <BannerLongRoundVector className="md:absolute lg:top-40 lg:left-15 lg:rotate-90 xl:top-40 xl:left-50 xl:scale-130" />
            <BannerRoundVector className="left-0 scale-120 md:absolute lg:top-40 xl:top-40 xl:left-10 xl:scale-160" />
          </AnimationFrameInOut>
        </div>
      </div>
    </div>
  );
}

export default Landing;
