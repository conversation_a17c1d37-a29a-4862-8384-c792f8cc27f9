'use client';
import HomepageSection2ImgMobile from '@hi7/assets/background/homepage-section2-img-mobile.png';
import HomepageSection2Img from '@hi7/assets/background/homepage-section2-img.jpg';
import SupportedPlatforms from '@hi7/components/SupportedPlatforms';
import type { DictionaryProps } from '@hi7/interface/i18n';
import clsx from 'clsx';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';
import TriggerAnimation from '../TriggerAnimation';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

function AppIntergration({ dictionary }: DictionaryProps) {
  return (
    <TriggerAnimation>
      <div className="relative flex flex-col md:flex-col lg:h-[99vh] lg:flex-col-reverse">
        <section className="relative mt-0 h-[500px] rounded-r-[40px] pb-20 md:h-[480px] lg:mt-0 lg:h-screen lg:rounded-r-[100px] lg:pb-0 xl:mt-0 xl:rounded-r-[130px] xl:pt-10">
          <div className="bg-[#90c4fc] pt-12 pr-6 pl-0 text-right lg:bg-transparent lg:pt-0 lg:pr-0 xl:pt-6 xl:pr-0">
            <div className="mb-9 lg:mb-8 lg:hidden xl:mb-9">
              <h1
                className={`text-[42px] font-semibold tracking-[1px] text-[#0F172A] lg:text-[64px] ${arsenal.className}`}
              >
                {dictionary.home.appintegration.title}
              </h1>
            </div>
            <p
              className="text-thin mt-2 mb-5 text-[18px] leading-[25px] tracking-[0px] text-gray-700 lg:mb-23 lg:hidden lg:text-[19px] xl:mt-0 xl:mb-42 xl:text-3xl xl:leading-[40px]"
              dangerouslySetInnerHTML={{
                __html: dictionary.home.appintegration.desc,
              }}
            />
            <div className="hidden lg:block">
              <div className="flex h-auto flex-row md:flex-row lg:h-[100%]">
                <div className="relative w-1/2 w-full md:w-2/3 lg:w-5/9">
                  <Image
                    src={HomepageSection2Img}
                    alt={'insight-img'}
                    className={clsx(
                      'mt-0 h-[30vh] rounded-r-[45px] border-t-25 border-r-25 border-b-25 border-white object-cover',
                      'md:mr-[140px] md:rounded-[70px] md:rounded-r-none md:border-r-0 md:border-l-25',
                      'lg:mx-0 lg:-ml-0 lg:h-[50vh] lg:translate-y-[-5%] lg:rounded-l-[0px] lg:rounded-r-[85px] lg:border-r-[45px] lg:border-b-[40px] lg:border-l-[0px]',
                      'xl:h-[55vh] xl:translate-y-[-20%]',
                    )}
                  />
                </div>
                <div
                  className={clsx(
                    'mt-20 mb-40 w-full rounded-b-[40px] bg-[#90c4fc]',
                    'md:-mb-10 md:mb-0 md:w-1/3 md:pr-10',
                    'lg:-mb-20 lg:w-4/9 lg:items-start lg:justify-start lg:rounded-t-[70px] lg:pt-10 lg:pr-22',
                    'xl:pl-20',
                  )}
                >
                  <div className="mb-9 lg:mb-5 xl:mb-9">
                    <h1
                      className={`text-[42px] font-semibold tracking-[1px] text-[#04227D] lg:text-[50px] ${arsenal.className}`}
                    >
                      {dictionary.home.appintegration.title}
                    </h1>
                  </div>
                  <p
                    className="text-thin mt-2 mb-5 text-[18px] leading-[25px] tracking-[0px] text-[#04227D] lg:mb-23 xl:mt-0 xl:mb-42 xl:text-2xl xl:leading-[32px]"
                    dangerouslySetInnerHTML={{
                      __html: dictionary.home.appintegration.desc,
                    }}
                  />
                </div>
              </div>
            </div>

            <div className="mt-4 flex flex-wrap justify-center gap-4 bg-[#90c4fc] pb-20 lg:-mt-25 lg:py-25 lg:pr-20 xl:-mt-[180px] xl:h-[59vh] xl:pr-20">
              <div className="absolute relative top-0 left-0 h-full w-full lg:top-10">
                <hr className="my-2 lg:mt-2 lg:mb-0 xl:mb-5" />
                <SupportedPlatforms location="1st" />
                <hr className="my-2 lg:mt-2 lg:mb-0 xl:mb-5" />
                <SupportedPlatforms location="2nd" />
                <hr className="my-2 lg:mt-2 lg:mb-0 xl:mt-3" />
              </div>
            </div>
          </div>
        </section>

        <section className="-mt-10 lg:mt-0 lg:hidden">
          <div className="overflow-hidden">
            <div className="flex h-auto flex-row md:h-[50%] md:flex-row lg:h-[130px]">
              <div className="relative w-1/2 w-[100vw] md:w-2/3 lg:w-5/9">
                <Image
                  src={HomepageSection2ImgMobile}
                  alt={'insight-img'}
                  className={clsx(
                    'mt-0 h-[40vh] rounded-r-[67px] border-t-35 border-r-25 border-b-25 border-white object-cover object-right',
                    'md:h-[35vh] md:w-full md:rounded-[70px] md:rounded-l-none md:border-r-25',
                    'lg:mx-50 lg:-ml-0 lg:h-[50vh] lg:translate-y-[-5%] lg:rounded-[85px] lg:border-r-[45px] lg:border-b-30 lg:border-l-35',
                    'xl:h-[45vh] xl:translate-y-[-20%]',
                  )}
                />
              </div>
              <div
                className={clsx(
                  'z-10 mb-60 flex w-[90%] items-end justify-end rounded-b-[40px] bg-[#90c4fc]',
                  'md:mb-50 md:w-1/3',
                  'lg:-mb-27 lg:w-4/9 lg:items-end lg:justify-start lg:rounded-t-[70px] lg:pr-0 lg:pl-16',
                  'xl:pl-20',
                )}
              ></div>
            </div>
          </div>
        </section>
      </div>
    </TriggerAnimation>
  );
}

export default AppIntergration;
