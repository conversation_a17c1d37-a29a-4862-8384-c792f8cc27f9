import BannerLongRoundVector from '@hi7/assets/background/elongated-round-vector.svg';
import BannerBgImg from '@hi7/assets/background/global-accqui-bg-img.png';
import BannerBgImgMobile from '@hi7/assets/background/global-accqui-bg-mobile-img.png';
import BannerRoundVector from '@hi7/assets/background/rounded-vector.svg';
import AnimationFrameInOut from '@hi7/components/AnimationFrameInOut';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal, Noto_Sans_SC } from 'next/font/google';
import Image from 'next/image';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

const notoSansSC = Noto_Sans_SC({
  subsets: ['latin'],
  weight: ['100', '300', '400', '700'],
  display: 'swap',
});

function Landing({ dictionary, locale }: DictionaryProps) {
  return (
    <div className="relative flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="h-screen md:h-[50vh] lg:h-[100vh]">
          <div className="w-full items-center justify-center lg:flex">
            <Image
              src={BannerBgImg}
              alt={'background'}
              className="absolute top-0 left-0 z-0 hidden md:block lg:h-[95vh] xl:w-screen"
            />
            <Image
              src={BannerBgImgMobile}
              alt={'background'}
              className="h-full w-full object-cover md:hidden"
            />
            <div className="animate-slide-in-down absolute top-20 left-10 z-10 w-[90%] text-start whitespace-break-spaces text-white transition-opacity duration-700 lg:top-30 xl:left-24">
              <h1
                className={`text-[35px] leading-[35px] font-bold tracking-[1px] lg:text-[64px] xl:mb-9 xl:text-[77px] xl:leading-[68px] ${arsenal.className}`}
              >
                {dictionary.features.globalDataAcquisition.banner.title}
              </h1>
              <hr className="my-4 w-[90%] md:mx-auto md:w-full lg:my-5 xl:my-8" />
              <div
                className={`grid items-center gap-2 lg:grid-cols-[20%_80%] lg:gap-5 lg:pl-5 ${locale === 'zh' ? 'grid-cols-[15%_85%]' : 'grid-cols-[30%_70%]'}`}
              >
                <div className="items-center justify-center self-center text-right">
                  <p
                    className={`${locale === 'zh' ? 'text-[18px]' : 'text-[14px]'} leading-[20px] font-[400] whitespace-pre-line lg:text-[20px] lg:whitespace-normal xl:text-[29px] xl:leading-[30px]`}
                  >
                    {
                      dictionary.features.globalDataAcquisition.banner
                        .point1Title
                    }
                  </p>
                </div>

                {locale === 'en' ? (
                  <h3 className="mb-0 flex flex-col items-baseline md:flex-row">
                    <span className="text-[28px] leading-[35px] font-bold lg:text-[48px] xl:text-[70px]">
                      200+
                    </span>
                    <span className="text-[20px] leading-[20px] font-thin lg:text-[48px] xl:text-[70px]">
                      {' '}
                      {
                        dictionary.features.globalDataAcquisition.banner
                          .point1Desc1
                      }
                    </span>
                    <span className="text-[12px] leading-[20px] font-normal lg:pl-2 lg:text-[20px] xl:text-[28px]">
                      {' '}
                      {
                        dictionary.features.globalDataAcquisition.banner
                          .point1Desc2
                      }
                    </span>
                  </h3>
                ) : (
                  <h3 className="mb-0 flex items-center">
                    <span className="text-[30px] leading-[35px] font-bold lg:text-[48px] xl:text-[70px]">
                      200+
                    </span>
                    <div className="flex flex-col justify-center lg:flex-row">
                      {' '}
                      <span
                        className={`text-[20px] leading-[20px] font-thin lg:text-[48px] xl:text-[70px] ${notoSansSC.className}`}
                      >
                        {' '}
                        {
                          dictionary.features.globalDataAcquisition.banner
                            .point1Desc1
                        }
                      </span>
                      <span className="text-[12px] leading-[20px] font-normal lg:pl-2 lg:text-[20px] xl:text-[28px]">
                        {' '}
                        {
                          dictionary.features.globalDataAcquisition.banner
                            .point1Desc2
                        }
                      </span>
                    </div>
                  </h3>
                )}
              </div>
              <hr className="my-4 w-[90%] md:mx-auto md:w-full lg:my-5 xl:my-8" />
              <div
                className={`grid items-center gap-2 lg:grid-cols-[20%_80%] lg:gap-5 lg:pl-5 ${locale === 'zh' ? 'grid-cols-[15%_85%]' : 'grid-cols-[30%_70%]'}`}
              >
                <div className="items-center justify-center self-center text-right">
                  <p
                    className={`${locale === 'zh' ? 'text-[18px]' : 'text-[14px]'} leading-[20px] font-[400] lg:text-[20px] xl:text-[29px] xl:leading-[30px]`}
                  >
                    {
                      dictionary.features.globalDataAcquisition.banner
                        .point2Title
                    }
                  </p>
                </div>

                {locale === 'en' ? (
                  <h3 className="mb-0 flex flex-col items-baseline md:flex-row">
                    <span className="text-[28px] leading-[35px] font-bold lg:text-[48px] xl:text-[70px]">
                      {' '}
                      {
                        dictionary.features.globalDataAcquisition.banner
                          .point2Desc1
                      }
                    </span>
                    <span className="text-[20px] leading-[20px] font-thin lg:text-[48px] xl:text-[70px]">
                      {' '}
                      {
                        dictionary.features.globalDataAcquisition.banner
                          .point2Desc2
                      }
                    </span>
                    <span className="text-[12px] leading-[20px] font-normal lg:pl-2 lg:text-[20px] xl:text-[28px]">
                      {' '}
                      {
                        dictionary.features.globalDataAcquisition.banner
                          .point2Desc3
                      }
                    </span>
                  </h3>
                ) : (
                  <h3 className="mb-0 flex items-center">
                    <span className="-ml-1 text-[30px] leading-[35px] font-bold lg:ml-0 lg:text-[48px] xl:text-[70px]">
                      {' '}
                      {
                        dictionary.features.globalDataAcquisition.banner
                          .point2Desc1
                      }
                    </span>
                    <div className="flex flex-col lg:flex-row">
                      {' '}
                      <span
                        className={`text-[20px] leading-[20px] font-thin lg:text-[48px] xl:text-[70px] ${notoSansSC.className}`}
                      >
                        {' '}
                        {
                          dictionary.features.globalDataAcquisition.banner
                            .point2Desc2
                        }
                      </span>
                      <span className="text-[12px] leading-[20px] font-normal lg:pl-2 lg:text-[20px] xl:text-[28px]">
                        {' '}
                        {
                          dictionary.features.globalDataAcquisition.banner
                            .point2Desc3
                        }
                      </span>
                    </div>
                  </h3>
                )}
              </div>
              <hr className="my-4 w-[90%] md:mx-auto md:w-full lg:my-5 xl:my-8" />
              <div
                className={`grid items-center gap-2 lg:grid-cols-[20%_80%] lg:gap-5 lg:pl-5 ${locale === 'zh' ? 'grid-cols-[15%_85%]' : 'grid-cols-[30%_70%]'}`}
              >
                <div className="items-center justify-center self-center text-right">
                  <p
                    className={`${locale === 'zh' ? 'text-[18px]' : 'text-[14px]'} leading-[20px] font-[400] lg:text-[20px] xl:text-[29px] xl:leading-[30px]`}
                  >
                    {
                      dictionary.features.globalDataAcquisition.banner
                        .point3Title
                    }
                  </p>
                </div>

                {locale === 'en' ? (
                  <h3 className="mb-0 flex flex-col items-baseline md:flex-row">
                    <span className="text-[28px] leading-[35px] font-bold lg:text-[45px] xl:text-[70px]">
                      {' '}
                      {
                        dictionary.features.globalDataAcquisition.banner
                          .point3Desc1
                      }
                    </span>
                    <span className="text-[20px] leading-[20px] font-thin lg:text-[45px] xl:text-[70px]">
                      {' '}
                      {
                        dictionary.features.globalDataAcquisition.banner
                          .point3Desc2
                      }
                    </span>
                    <span className="text-[12px] leading-[20px] font-normal lg:text-[20px] xl:text-[24px]">
                      {' '}
                      {
                        dictionary.features.globalDataAcquisition.banner
                          .point3Desc3
                      }
                    </span>
                  </h3>
                ) : (
                  <h3 className="mb-0 flex items-center">
                    <span className="text-[28px] leading-[35px] font-bold lg:text-[48px] xl:text-[70px]">
                      {' '}
                      {
                        dictionary.features.globalDataAcquisition.banner
                          .point3Desc1
                      }
                    </span>
                    <div className="flex flex-col lg:flex-row">
                      {' '}
                      <span
                        className={`text-[20px] leading-[20px] font-thin lg:text-[48px] xl:text-[70px] ${notoSansSC.className}`}
                      >
                        {' '}
                        {
                          dictionary.features.globalDataAcquisition.banner
                            .point3Desc2
                        }
                      </span>
                      <span className="text-[12px] leading-[20px] font-normal lg:text-[20px] xl:text-[24px]">
                        {' '}
                        {
                          dictionary.features.globalDataAcquisition.banner
                            .point3Desc3
                        }
                      </span>
                    </div>
                  </h3>
                )}
              </div>
              <hr className="my-4 w-[100%] md:mx-auto md:w-full lg:my-5 xl:my-8" />
              <div className="block lg:flex">
                <a
                  href="https://t.me/Abb007tgbot"
                  target="_blank"
                  className="mt-5 block w-auto max-w-[160px] cursor-pointer rounded-[25px] bg-[#FF5542] px-[20px] py-1 text-center text-[15px] leading-[40px] font-bold whitespace-nowrap text-white hover:bg-[#C7E5FF] lg:max-w-[200px] lg:text-[18px]"
                >
                  {dictionary.general.freeTrial.button1}
                </a>
                <a
                  href="https://mall.007tg.com/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mt-5 block w-auto max-w-[160px] cursor-pointer rounded-[25px] bg-[#047AFF] px-[20px] py-1 text-center text-[15px] leading-[40px] font-bold whitespace-nowrap text-white hover:bg-[#C7E5FF] lg:ml-5 lg:max-w-[200px] lg:text-[18px]"
                >
                  {dictionary.general.freeTrial.button5}
                </a>
              </div>
            </div>
            <BannerLongRoundVector className="absolute -right-43 -bottom-13 scale-55 md:hidden" />
            <BannerRoundVector className="absolute right-16 bottom-10 scale-55 md:hidden" />
          </div>
          <AnimationFrameInOut
            variant="SlideInRight45Degree"
            outVariant="SlideInRight45DegreeFurther"
            once={false}
            className="hidden lg:block"
          >
            <BannerLongRoundVector className="md:absolute lg:top-115 lg:right-80 lg:scale-70 xl:top-200 xl:left-330 xl:scale-130" />
            <BannerLongRoundVector className="md:absolute lg:top-115 lg:right-25 lg:scale-70 xl:top-200 xl:left-240 xl:scale-130" />
            <BannerRoundVector className="right-0 scale-80 md:absolute lg:top-125 lg:right-15 xl:top-190 xl:left-410 xl:scale-130" />
          </AnimationFrameInOut>
        </div>
      </div>
    </div>
  );
}

export default Landing;
